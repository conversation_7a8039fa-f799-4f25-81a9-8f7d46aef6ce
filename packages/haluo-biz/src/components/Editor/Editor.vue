<template>
  <div class="editor">
    <!-- <button @click="getHtml">获取html</button>
    <button @click="setHtml">设置html</button> -->
    <!-- <button @click="updateTopicPosition">updateTopicPosition</button> -->
    <div
      v-show="!isInputing && !titleCount && !hasArticleCard"
      :style="{ textAlign: align }"
      class="placeholder"
    >
      请输入正文
    </div>
    <div v-if="overLine" class="overline" :style="{ top: overLine }"></div>
    <!-- 富文本正文 -->
    <div
      id="editor-content"
      :style="{ cursor: cursorStyle }"
      @keyup="setBack($event)"
    ></div>
    <el-dialog
      v-model="viewLinkDialog"
      :before-close="handleClose"
      class="squire-dialog-content"
      append-to-body
    >
      <div class="add-link">
        <el-form :model="linkForm">
          <el-form-item label="添加链接" class="link-title">
            <el-input
              v-model="linkForm.linkAddress"
              placeholder="请输入链接地址"
            />
          </el-form-item>
          <el-form-item label="链接文案">
            <el-input
              v-model="linkForm.linkWriting"
              type="textarea"
              autosize
              placeholder="请输入链接文案"
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewLinkDialog = false">取 消</el-button>
        <el-button type="primary" @click="insertLink">确 定</el-button>
      </div>
    </el-dialog>
    <InsertArticle
      :article="article"
      v-model:visible="visibleArticle"
      :getList="getArticleList"
      @delete="handleImgDelete"
      @change="selectArticle"
    ></InsertArticle>
    <ImgUpload
      v-model:img-list="imgList"
      v-model:visible="visibleImg"
      :chartGallery="chartGallery"
      :request="request"
      :imgNum="imgNum"
      @submit="submitImg"
    />
    <VideoUpload v-model:visible="visibleVideo" :file-selected="uploadVideo" />
    <CollectArticle
      v-model:link-content="linkContent"
      v-model:visible="visibleCollectArticle"
      @submit="confirmLink"
    />
    <input
      type="file"
      name="file"
      multiple
      class="upload-again-input hide"
      accept="image/*"
      @change="againImg"
    />
    <input
      type="file"
      name="file"
      class="replace-input hide"
      accept="image/*"
      @change="replaceImg"
    />
    <input
      type="file"
      name="file"
      class="replace-poster hide"
      accept="image/*"
      @change="replacePoster"
    />

    <!-- 话题弹框 -->
    <div
      v-if="topicPopover.visible"
      class="topic-popover"
      :style="{
        top: topicPopover.position.top + 'px',
        left: topicPopover.position.left + 'px'
      }"
      @click.stop
    >
      <!-- 热门话题弹框 -->
      <div v-if="topicPopover.type === 'hot'" class="topic-popover-content">
        <div class="topic-tabs">
          <div
            class="topic-tab"
            :class="{ active: topicPopover.activeTab === 'hot' }"
            @click="switchTopicTab('hot')"
          >
            热门话题
          </div>
          <div
            class="topic-tab"
            :class="{ active: topicPopover.activeTab === 'recent' }"
            @click="switchTopicTab('recent')"
          >
            最近使用
          </div>
        </div>
        <div class="topic-list-container" @scroll="handleTopicScroll">
          <!-- 热门话题列表 -->
          <div v-if="topicPopover.activeTab === 'hot'" class="topic-list">
            <div v-if="topicPopover.loading && topicPopover.hotTopics.length === 0" class="topic-loading">加载中...</div>
            <div v-else-if="topicPopover.hotTopics.length === 0" class="topic-empty">暂无热门话题</div>
            <div v-else>
              <div
                v-for="topic in topicPopover.hotTopics"
                :key="topic.id"
                class="topic-item"
                @click="selectTopic(topic)"
              >
                #{{ topic.exactlyMatchTitle }}
              </div>
              <div v-if="topicPopover.loading" class="topic-loading">加载更多...</div>
              <div v-else-if="!topicPopover.hasMore" class="topic-empty">没有更多了</div>
            </div>
          </div>
          <!-- 最近使用列表 -->
          <div v-if="topicPopover.activeTab === 'recent'" class="topic-list">
            <div v-if="topicPopover.recentTopics.length === 0" class="topic-empty">暂无最近使用记录</div>
            <div v-else>
              <div
                v-for="topic in topicPopover.recentTopics"
                :key="topic.id"
                class="topic-item"
                @click="selectTopic(topic)"
              >
                #{{ topic.exactlyMatchTitle }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索话题弹框 -->
      <div v-if="topicPopover.type === 'search'" class="topic-popover-content">
        <div class="topic-search-header">
          <span class="topic-search-title">#{{ topicPopover.searchKeyword }}</span>
        </div>
        <div class="topic-list-container" @scroll="handleSearchTopicScroll">
          <div class="topic-list">
            <div v-if="topicPopover.loading && topicPopover.searchTopics.length === 0" class="topic-loading">搜索中...</div>
            <div v-else-if="topicPopover.searchTopics.length === 0" class="topic-empty">没有匹配到话题，请重新输入</div>
            <div v-else>
              <div
                v-for="topic in topicPopover.searchTopics"
                :key="topic.id"
                class="topic-item"
                @click="selectTopic(topic)"
              >
                #{{ topic.exactlyMatchTitle }}
              </div>
              <div v-if="topicPopover.loading" class="topic-loading">加载更多...</div>
              <div v-else-if="!topicPopover.searchHasMore" class="topic-empty">没有更多了</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 点击空白处关闭弹框的遮罩 -->
    <div
      v-if="topicPopover.visible"
      class="topic-popover-mask"
      @click="hideTopicPopover"
    ></div>
  </div>
</template>
<script>
/**
 * 示例：
 * <editor ref="editor" @backData="backData" />
 * this.$refs.editor.initData(data);
 * this.$refs.editor.getEditorData();
 */
// const lrz = require('lrz');
// 方式二
import {
  ElInput,
  ElFormItem,
  ElForm,
  ElDialog,
  ElButton,
  ElMessage,
  ElMessageBox,
} from 'element-plus'

import { h, render } from 'vue'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import util from '@haluo/util' // 局部引用
import InsertArticle from './insert-article/index.vue'
import ImgUpload from './img-upload/index.vue'
import VideoUpload from './video-upload/index.vue'
import CollectArticle from './collect-article/index.vue'
import { mountArticleDom } from './article-item/mount.js'
import car_ciose from '@/assets/img/tools/<EMAIL>'
const cursorImg = `url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAYCAMAAACoeN87AAAAh1BMVEUAAAACAgL39/cICAgFBQUAAAADAwMKCgr6+vrn5+fr6+v6+voEBAT19fXm5ubf39+pqanBwcH8/PzOzs76+vrx8fHz8/P29vbh4eHm5ubT09OXl5f///9ZWVkiIiLIyMjGxsY+Pj5LS0uenp4vLy/V1dXk5OSsrKxnZ2eRkZF1dXXj4+ODg4P4y9kGAAAAHHRSTlMACMUDDQYOCuONi8MSt2tJMCryT9OpqKaRezobrP6h8wAAAR9JREFUKM+dk1lygzAMQOMtxoQlS/dKBrMn7f3PVwEFPND0I+/Dg8RD0mjM7nGEZoIOqfwc+0jTVE+RgcAcTQBvbFFOvM7zJvgcIw2uaKF0DuRS5LW1RBmKMQyhdugyMGJWGMciywrkauobZrY2kgxPQQCcFGIPFvb+sNGkSLUo3iDqEvPzqJz5IVF/KEnTIVak0IG35rJV1OFGr7AEKJHULh7KRKRE86gHLPKZAjnrkwEpgV4U6zEqEugRpL+TmXtVEGaQlO0sfK1sVnds7Pf/yim42q9FccjVai/q5WornKlsm4hVFRGX6NFlT7rPSlPb3GgxNnrmHvF7nxYh5A5dDaEaWzGf4TsGzpXQFsut2yDC37tr1O4eSurhD6CaPzWzJeCQ3tPtAAAAAElFTkSuQmCC) 28 10, text`
const keyToEditorMethod = {
  bold: 'bold',
  italic: 'italic',
  underline: 'underline',
  size: 'setFontSize',
  color: 'setTextColour',
}
export default {
  name: 'Edit',
  components: {
    ElInput,
    InsertArticle,
    ImgUpload,
    VideoUpload,
    CollectArticle,
    ElFormItem,
    ElForm,
    ElDialog,
    ElButton,
  },
  props: [
    'disabled',
    'isOss',
    'getEassyDetail',
    'getArticleList',
    'importEssay',
    'uploadImageByOther',
    'chartGallery',
    'request',
  ],
  data() {
    return {
      hasArticleCard: false,
      cursorStyle: 'auto',
      titleCount: 0, // 标题数量
      viewLinkDialog: false,
      linkForm: {
        linkAddress: '',
        linkWriting: '',
      },
      setAlignFlag: true,
      article: {}, // 插入的文章
      imgList: [],
      imgNum: 20,
      videoList: [],
      visibleVideo: false,
      visibleCollectArticle: false,
      visibleArticle: false, // 插入文章
      visibleImg: false, // 插入图片
      imgType: 'normal', //
      typeEnum: {
        文字: '1',
        图片: '2',
        图文: '3',
        段落标题: '4',
        关联: '5', // 关联车辆、轨迹、活动、商家、话题
        单视频: '6',
        视频文字: '7',
        URL: '8',
        文章卡片: '11',
      },
      fontInfo: {
        size: '',
      },
      user: {}, // 用户
      editor: {}, // 编辑器实例对象
      editorDom: {}, // 编辑器Dom
      uploadStore: {}, // 待上传的图片池
      linkContent: '', // 插入链接的地址
      loadingText: '', // loaing的提示文字
      progressPercent: '', // 上传进度
      currentIndex: 0,
      loading: false, // 是否提交中
      viewStatus: false, // todo: 改名
      linkStatus: false, // todo: 改名
      styleStatus: {
        bold: false,
        italic: false,
        size: '',
        color: '',
        underline: false,
        title: false,
      },
      contentLen: 0,
      imgCount: 0,
      backState: {
        redo: false,
        undo: false,
      },
      countList: [],
      replaceSeamless: 0,
      seamlessCount: 0,
      overLine: '',
      lasteDropDom: '',
      currentVideo: null,
      targetMove: '',
      moverClasses: ['halo-img-content', 'halo-video-content'],
      selectDom: null, //选中的dom
      // 话题弹框相关数据
      topicPopover: {
        visible: false,
        type: 'hot', // 'hot' 热门话题, 'search' 搜索话题
        activeTab: 'hot', // 'hot' 热门话题, 'recent' 最近使用
        position: { top: 0, left: 0 },
        searchKeyword: '',
        hotTopics: [],
        searchTopics: [],
        recentTopics: [],
        loading: false,
        page: 1,
        hasMore: true,
        searchPage: 1,
        searchHasMore: true,
        // 保存原始的selection和range信息
        originalRange: null,
        originalSelection: null,
        // 保存触发位置信息
        triggerInfo: {
          paragraph: null,
          hashIndex: -1,
          cursorPosition: 0
        }
      },
    }
  },
  computed: {
    align() {
      if (!this.isInputing && !this.titleCount && !this.hasArticleCard) {
        return this.getCursorAlignStyle() || 'left'
      }
      return 'left'
    },
    isInputing() {
      return this.contentLen > 0 || this.imgCount > 0
    },
  },
  watch: {
    progressPercent(val) {
      const inner = document.querySelector('.video-progress .inner')
      console.log(val, '视频上传进度')
      if (inner) {
        inner.style.width = val + '%'
        if (val === 100) {
          document.querySelector('.video-progress').innerHTML = `
            <div class="no-calc" style="text-align:center;color:#999999;font-size:18px;">正在获取视频封面</div>
          `
        }
      }
    },
    disabled: {
      handler(val) {
        if (val) {
          this.editorDom.contentEditable = 'false'
        }
      },
      immediate: true,
    },
  },
  created() {
    const me = this
    me.user = JSON.parse(
      window.localStorage.getItem('user') ||
        window.localStorage.getItem('userInfo') ||
        '{}'
    )
    me.guid = me.user.uid + '|' + util.tools.guid()
  },
  beforeDestroy() {
    this.editorDom &&
      this.editorDom.removeEventListener('blur', this.canSetAlign)
  },
  mounted() {
    const me = this
    me.editorDom = document.getElementById('editor-content')
    me.editorDom.addEventListener('blur', this.canSetAlign)
    if (!window.Squire) {
      import('../../assets/js/squire-raw.js').then(function () {
        me.initSquire()
      })
    } else {
      me.initSquire()
    }
  },
  methods: {
    // 调整插入位置，确保不在话题空格内部插入
    adjustInsertionPosition(range) {
      const container = range.startContainer
      const offset = range.startOffset

      // 检查是否在话题空格文本节点内部
      if (container.nodeType === Node.TEXT_NODE &&
          container.textContent === '\u00A0' &&
          container.previousSibling &&
          container.previousSibling.tagName === 'MDD-TOPIC') {
        // 在话题空格文本节点内部，需要调整到空格后面
        const newRange = document.createRange()
        newRange.setStartAfter(container)
        newRange.collapse(true)
        return newRange
      }

      // 如果不在话题空格内部，返回原始range
      return range
    },

    // 检查是否会创建嵌套的halo-paragraph
    wouldCreateNestedParagraph(range) {
      let container = range.startContainer

      // 向上查找，看是否已经在halo-paragraph内部
      while (container && container !== this.editorDom) {
        if (container.nodeType === Node.ELEMENT_NODE &&
            container.classList &&
            container.classList.contains('halo-paragraph')) {
          return true
        }
        container = container.parentNode
      }

      return false
    },

    // 检查光标是否在两个连续的mdd-topic之间
    isBetweenTwoTopics(range) {
      const container = range.startContainer
      const offset = range.startOffset

      // 如果不是文本节点，检查相邻的元素
      if (container.nodeType === Node.ELEMENT_NODE) {
        const prevElement = container.childNodes[offset - 1]
        const nextElement = container.childNodes[offset]

        return (prevElement && prevElement.tagName === 'MDD-TOPIC') &&
               (nextElement && nextElement.tagName === 'MDD-TOPIC')
      }

      // 如果是文本节点，检查是否是空文本且在两个topic之间
      if (container.nodeType === Node.TEXT_NODE && container.textContent.trim() === '') {
        const prevSibling = container.previousSibling
        const nextSibling = container.nextSibling

        return (prevSibling && prevSibling.tagName === 'MDD-TOPIC') &&
               (nextSibling && nextSibling.tagName === 'MDD-TOPIC')
      }

      return false
    },

    // 检查光标是否在话题前面（用于处理连续话题中间的回车）
    isBeforeTopic(range) {
      const container = range.startContainer
      const offset = range.startOffset

      // 找到当前段落
      let currentParagraph = container
      while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
        currentParagraph = currentParagraph.parentNode
      }
      while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
        currentParagraph = currentParagraph.parentNode
      }

      if (!currentParagraph) return false

      // 获取段落中的所有话题元素
      const topics = currentParagraph.querySelectorAll('mdd-topic')
      if (topics.length < 2) return false

      // 检查光标是否在某个话题前面（除了第一个话题）
      for (let i = 1; i < topics.length; i++) {
        const topic = topics[i]

        // 检查光标是否在这个话题前面
        if (this.isCursorBeforeElement(range, topic)) {
          return true
        }
      }

      return false
    },

    // 检查光标是否在指定元素前面
    isCursorBeforeElement(range, element) {
      const container = range.startContainer
      const offset = range.startOffset

      // 如果光标在元素节点中
      if (container.nodeType === Node.ELEMENT_NODE) {
        const elementIndex = Array.from(container.childNodes).indexOf(element)
        return offset <= elementIndex
      }

      // 如果光标在文本节点中
      if (container.nodeType === Node.TEXT_NODE) {
        // 检查文本节点是否在元素前面
        let currentNode = container
        while (currentNode) {
          if (currentNode === element) {
            return true
          }
          if (currentNode.nextSibling === element) {
            // 如果光标在文本节点的末尾，且下一个兄弟节点是目标元素
            return offset >= container.textContent.length
          }
          currentNode = currentNode.nextSibling
        }
      }

      return false
    },

    // 检查是否在话题边界位置（话题前面或话题之间）
    isAtTopicBoundary(range, currentParagraph) {
      const container = range.startContainer
      const offset = range.startOffset

      // 如果不在段落中，返回false
      if (!currentParagraph) {
        currentParagraph = this.getCurrentParagraph(range)
      }
      if (!currentParagraph) return false

      // 获取段落中的所有话题
      const topics = currentParagraph.querySelectorAll('mdd-topic')
      if (topics.length === 0) return false

      // 检查光标是否在话题前面或话题之间
      if (container.nodeType === Node.TEXT_NODE) {
        // 如果是话题空格文本节点，算作话题边界
        if (container.textContent === '\u00A0' &&
            container.previousSibling &&
            container.previousSibling.tagName === 'MDD-TOPIC') {
          return true
        }

        // 检查文本节点后面是否紧跟话题
        let nextSibling = container.nextSibling
        while (nextSibling) {
          if (nextSibling.nodeType === Node.ELEMENT_NODE &&
              nextSibling.tagName === 'MDD-TOPIC') {
            return true
          }
          // 跳过空的文本节点和话题空格
          if (nextSibling.nodeType === Node.TEXT_NODE &&
              (!nextSibling.textContent.trim() || nextSibling.textContent === '\u00A0')) {
            nextSibling = nextSibling.nextSibling
            continue
          }
          break
        }
      } else if (container.nodeType === Node.ELEMENT_NODE) {
        // 在元素节点中，检查指定位置是否在话题前面
        if (offset < container.childNodes.length) {
          const nodeAtOffset = container.childNodes[offset]
          if (nodeAtOffset && nodeAtOffset.tagName === 'MDD-TOPIC') {
            return true
          }
        }
      }

      return false
    },

    // 处理话题边界的回车
    handleTopicBoundaryEnter(range, currentParagraph) {
      // 创建新段落
      const newParagraph = document.createElement('p')
      newParagraph.className = 'halo-paragraph'

      // 提取光标后的内容
      const afterContent = this.extractContentAfterCursor(range, currentParagraph)

      if (afterContent && afterContent.childNodes.length > 0) {
        while (afterContent.firstChild) {
          newParagraph.appendChild(afterContent.firstChild)
        }
      } else {
        newParagraph.innerHTML = '<br>'
      }

      // 插入新段落
      currentParagraph.parentNode.insertBefore(newParagraph, currentParagraph.nextSibling)

      // 将光标移动到新段落开始
      const newRange = document.createRange()
      if (newParagraph.firstChild && newParagraph.firstChild.nodeType === Node.TEXT_NODE) {
        newRange.setStart(newParagraph.firstChild, 0)
      } else if (newParagraph.firstChild && newParagraph.firstChild.tagName === 'MDD-TOPIC') {
        newRange.setStart(newParagraph, 0)
      } else {
        newRange.setStart(newParagraph, 0)
      }
      newRange.collapse(true)

      const selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(newRange)

      this.updateData(true)
    },

    // 处理两个话题之间的回车或话题前面的回车
    handleEnterBetweenTopics(range) {
      const me = this

      // 找到当前段落
      let currentParagraph = range.startContainer
      while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
        currentParagraph = currentParagraph.parentNode
      }
      while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
        currentParagraph = currentParagraph.parentNode
      }

      if (!currentParagraph) return

      // 创建新段落
      const newParagraph = document.createElement('p')
      newParagraph.className = 'halo-paragraph'

      // 更精确地提取光标后的内容
      const afterContent = me.extractContentAfterCursorPrecise(range, currentParagraph)

      if (afterContent && afterContent.childNodes.length > 0) {
        // 将提取的内容添加到新段落
        while (afterContent.firstChild) {
          newParagraph.appendChild(afterContent.firstChild)
        }
      } else {
        newParagraph.innerHTML = '<br>'
      }

      // 插入新段落
      currentParagraph.parentNode.insertBefore(newParagraph, currentParagraph.nextSibling)

      // 将光标移动到新段落的开始位置
      const newRange = document.createRange()
      if (newParagraph.firstChild && newParagraph.firstChild.nodeType === Node.TEXT_NODE) {
        newRange.setStart(newParagraph.firstChild, 0)
      } else if (newParagraph.firstChild && newParagraph.firstChild.nodeType === Node.ELEMENT_NODE) {
        newRange.setStart(newParagraph, 0)
      } else {
        newRange.setStart(newParagraph, 0)
      }
      newRange.collapse(true)

      const selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(newRange)

      // 更新编辑器状态
      me.updateData(true)
    },

    // 更精确地提取光标后的内容（专门用于话题分割）
    extractContentAfterCursorPrecise(range, paragraph) {
      const fragment = document.createDocumentFragment()
      const container = range.startContainer
      const offset = range.startOffset

      // 如果光标在元素节点中
      if (container.nodeType === Node.ELEMENT_NODE) {
        // 从光标位置开始，将后面的所有子节点移动到fragment中
        const childNodes = Array.from(container.childNodes)
        for (let i = offset; i < childNodes.length; i++) {
          fragment.appendChild(childNodes[i])
        }
      }
      // 如果光标在文本节点中
      else if (container.nodeType === Node.TEXT_NODE) {
        // 分割文本节点
        if (offset < container.textContent.length) {
          const afterText = container.textContent.slice(offset)
          container.textContent = container.textContent.slice(0, offset)

          if (afterText.trim()) {
            const textNode = document.createTextNode(afterText)
            fragment.appendChild(textNode)
          }
        }

        // 将文本节点后面的所有兄弟节点移动到fragment中
        let nextSibling = container.nextSibling
        while (nextSibling) {
          const nodeToMove = nextSibling
          nextSibling = nextSibling.nextSibling
          fragment.appendChild(nodeToMove)
        }
      }

      return fragment
    },

    // 处理包含话题的段落中的回车键
    handleEnterKeyInParagraphWithTopic(range) {
      const me = this

      // 找到当前段落
      let currentParagraph = range.startContainer
      while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
        currentParagraph = currentParagraph.parentNode
      }
      while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
        currentParagraph = currentParagraph.parentNode
      }

      if (!currentParagraph) return

      // 创建新的段落
      const newParagraph = document.createElement('p')
      newParagraph.className = 'halo-paragraph'

      // 检查光标是否在段落末尾
      const isAtEnd = me.isCursorAtEndOfParagraph(range, currentParagraph)

      // 特殊处理：检查是否在话题行首回车（光标在话题前面）
      const isAtTopicStart = me.isCursorBeforeFirstTopic(range, currentParagraph)

      if (isAtEnd) {
        // 在段落末尾，直接插入新段落
        newParagraph.innerHTML = '<br>'
        currentParagraph.parentNode.insertBefore(newParagraph, currentParagraph.nextSibling)
      } else if (isAtTopicStart) {
        console.log('🔍 [ENTER-TOPIC-START] Handling enter at topic start')

        // 在话题前面回车，创建空段落并将话题移动到新段落
        const afterContent = me.extractContentAfterCursor(range, currentParagraph)

        console.log('🔍 [ENTER-TOPIC-START] After content:', afterContent);
        if (afterContent && afterContent.childNodes.length > 0) {
          // 将话题及后续内容移动到新段落
          while (afterContent.firstChild) {
            newParagraph.appendChild(afterContent.firstChild)
          }
          console.log('🔍 [ENTER-TOPIC-START] Moved content to new paragraph')
        } else {
          // 如果没有内容可移动，确保新段落有<br>
          newParagraph.innerHTML = '<br>'
          console.log('🔍 [ENTER-TOPIC-START] Added <br> to new paragraph (no content)')
        }

        // 当前段落变为空段落，添加<br>
        if (currentParagraph.innerHTML.trim() === '' || currentParagraph.childNodes.length === 0) {
          currentParagraph.innerHTML = '<br>'
          console.log('🔍 [ENTER-TOPIC-START] Added <br> to current paragraph')
        }

        // 插入新段落
        currentParagraph.parentNode.insertBefore(newParagraph, currentParagraph.nextSibling)
        console.log('🔍 [ENTER-TOPIC-START] Inserted new paragraph')
        console.log('🔍 [ENTER-TOPIC-START] Current paragraph HTML:', currentParagraph.outerHTML)
        console.log('🔍 [ENTER-TOPIC-START] New paragraph HTML:', newParagraph.outerHTML)
      } else {
        // 在段落中间，需要分割段落
        const afterContent = me.extractContentAfterCursor(range, currentParagraph)

        // 如果有内容需要移动到新段落
        if (afterContent && afterContent.childNodes.length > 0) {
          // 将光标后的内容移动到新段落
          while (afterContent.firstChild) {
            newParagraph.appendChild(afterContent.firstChild)
          }
        } else {
          newParagraph.innerHTML = '<br>'
        }

        // 插入新段落
        currentParagraph.parentNode.insertBefore(newParagraph, currentParagraph.nextSibling)
      }

      // 将光标移动到新段落的开始位置
      const newRange = document.createRange()
      if (newParagraph.firstChild && newParagraph.firstChild.nodeType === Node.TEXT_NODE) {
        newRange.setStart(newParagraph.firstChild, 0)
      } else if (newParagraph.firstChild) {
        newRange.setStart(newParagraph, 0)
      } else {
        newRange.setStart(newParagraph, 0)
      }
      newRange.collapse(true)

      const selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(newRange)

      // 更新编辑器状态
      me.updateData(true)
    },

    // 检查光标是否在第一个话题前面
    isCursorBeforeFirstTopic(range, paragraph) {
      console.log('🔍 [TOPIC-START] Checking if cursor is before first topic')
      const container = range.startContainer
      const offset = range.startOffset

      console.log('🔍 [TOPIC-START] Container:', container, 'Offset:', offset)
      console.log('🔍 [TOPIC-START] Paragraph:', paragraph.outerHTML)

      // 检查段落的第一个子元素是否是mdd-topic
      const firstChild = paragraph.firstChild
      console.log('🔍 [TOPIC-START] First child:', firstChild, 'Tag:', firstChild?.tagName)

      if (!firstChild || firstChild.tagName !== 'MDD-TOPIC') {
        console.log('🔍 [TOPIC-START] No topic as first child, returning false')
        return false
      }

      // 检查光标是否在话题前面
      if (container === paragraph && offset === 0) {
        console.log('🔍 [TOPIC-START] Cursor at paragraph start, returning true')
        return true
      }

      // 检查是否在话题前的文本节点中
      if (container.nodeType === Node.TEXT_NODE &&
          container.nextSibling === firstChild &&
          offset === 0) {
        console.log('🔍 [TOPIC-START] Cursor at start of text node before topic, returning true')
        return true
      }

      // 检查是否在段落开始但在话题之前的任何位置
      if (container === paragraph && offset === 0) {
        console.log('🔍 [TOPIC-START] Cursor at paragraph start with topic as first child, returning true')
        return true
      }

      // 检查是否在空的文本节点中且该节点在话题前面
      if (container.nodeType === Node.TEXT_NODE &&
          container.textContent === '' &&
          container.nextSibling === firstChild) {
        console.log('🔍 [TOPIC-START] Cursor in empty text node before topic, returning true')
        return true
      }

      console.log('🔍 [TOPIC-START] Not before first topic, returning false')
      return false
    },

    // 检查光标是否在段落末尾
    isCursorAtEndOfParagraph(range, paragraph) {
      const walker = document.createTreeWalker(
        paragraph,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: function(node) {
            // 跳过mdd-topic元素内的文本节点
            let parent = node.parentNode
            while (parent && parent !== paragraph) {
              if (parent.tagName === 'MDD-TOPIC') {
                return NodeFilter.FILTER_REJECT
              }
              parent = parent.parentNode
            }
            return NodeFilter.FILTER_ACCEPT
          }
        },
        false
      )

      let totalLength = 0
      let node
      while (node = walker.nextNode()) {
        totalLength += node.textContent.length
      }

      // 获取当前光标位置
      const { cursorPosition } = this.getParagraphTextExcludingTopics(paragraph, range)

      return cursorPosition >= totalLength
    },

    // 检查删除操作是否会导致嵌套段落问题
    wouldDeleteCauseNestedParagraph(range, deleteKey) {
      console.log('🔍 wouldDeleteCauseNestedParagraph called with:', deleteKey)

      const container = range.startContainer
      const offset = range.startOffset
      console.log('🔍 Container:', container, 'Offset:', offset)

      // 只有在特定情况下才需要特殊处理：
      // 1. 删除操作可能影响到话题元素的边界
      // 2. 删除操作在空段落中进行

      // 找到当前段落
      let currentParagraph = container
      while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
        currentParagraph = currentParagraph.parentNode
      }
      while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
        currentParagraph = currentParagraph.parentNode
      }

      if (!currentParagraph) {
        console.log('🔍 No current paragraph found, returning false')
        return false
      }

      // 检查是否是空段落（只包含<br>标签）
      const paragraphContent = currentParagraph.innerHTML.trim()
      console.log('🔍 Paragraph content check:', paragraphContent)
      if (paragraphContent === '<br>' || paragraphContent === '') {
        console.log('🔍 Empty paragraph detected, returning true')
        return true
      }

      // 检查是否在话题元素的边界位置进行删除
      if (container.nodeType === Node.TEXT_NODE) {
        console.log('🔍 Container is text node, checking topic boundaries')
        // 检查删除操作是否会影响到相邻的话题元素
        if (deleteKey === 'Backspace' && offset === 0) {
          // 在文本节点开头删除，检查前面是否有话题
          const prevSibling = container.previousSibling
          console.log('🔍 Backspace at start, prev sibling:', prevSibling)
          if (prevSibling && prevSibling.tagName === 'MDD-TOPIC') {
            console.log('🔍 Previous sibling is topic, returning true')
            return true
          }
        } else if (deleteKey === 'Delete' && offset === container.textContent.length) {
          // 在文本节点末尾删除，检查后面是否有话题
          const nextSibling = container.nextSibling
          console.log('🔍 Delete at end, next sibling:', nextSibling)
          if (nextSibling && nextSibling.tagName === 'MDD-TOPIC') {
            console.log('🔍 Next sibling is topic, returning true')
            return true
          }
        }
      }

      console.log('🔍 No special handling needed, returning false')
      return false
    },

    // 处理在话题上下文中的删除操作
    handleDeleteInTopicContext(range, deleteKey) {
      console.log('🔍 handleDeleteInTopicContext called with:', deleteKey)

      const container = range.startContainer
      const offset = range.startOffset
      console.log('🔍 Container:', container, 'Offset:', offset)

      // 找到当前段落
      let currentParagraph = container
      while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
        currentParagraph = currentParagraph.parentNode
      }
      while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
        currentParagraph = currentParagraph.parentNode
      }

      if (!currentParagraph) {
        console.log('🔍 No current paragraph found, returning false')
        return false
      }

      // 检查是否是空段落的删除操作
      const paragraphContent = currentParagraph.innerHTML.trim()
      console.log('🔍 Paragraph content:', paragraphContent)
      if (paragraphContent === '<br>' || paragraphContent === '') {
        console.log('🔍 Empty paragraph detected, letting editor handle naturally')
        // 对于空段落，让编辑器自然处理，不进行特殊干预
        return false
      }

      // 只处理可能影响话题边界的删除操作
      if (container.nodeType === Node.TEXT_NODE) {
        console.log('🔍 Container is text node, processing delete')
        if (deleteKey === 'Backspace' && offset > 0) {
          console.log('🔍 Processing backspace in text')
          // 删除前一个字符
          const newText = container.textContent.slice(0, offset - 1) + container.textContent.slice(offset)
          container.textContent = newText

          // 设置新的光标位置
          const newRange = document.createRange()
          newRange.setStart(container, offset - 1)
          newRange.collapse(true)

          const selection = window.getSelection()
          selection.removeAllRanges()
          selection.addRange(newRange)

          console.log('🔍 Backspace handled, returning true')
          return true // 表示已处理
        } else if (deleteKey === 'Delete' && offset < container.textContent.length) {
          console.log('🔍 Processing delete in text')
          // 删除后一个字符
          const newText = container.textContent.slice(0, offset) + container.textContent.slice(offset + 1)
          container.textContent = newText

          // 光标位置保持不变
          const newRange = document.createRange()
          newRange.setStart(container, offset)
          newRange.collapse(true)

          const selection = window.getSelection()
          selection.removeAllRanges()
          selection.addRange(newRange)

          console.log('🔍 Delete handled, returning true')
          return true // 表示已处理
        }
      }

      console.log('🔍 No handling needed, returning false')
      return false // 未处理，让其他逻辑继续
    },

    // 获取当前段落
    getCurrentParagraph(range) {
      let container = range.startContainer

      while (container && container !== this.editorDom) {
        if (container.nodeType === Node.ELEMENT_NODE &&
            container.classList &&
            container.classList.contains('halo-paragraph')) {
          return container
        }
        container = container.parentNode
      }

      return null
    },

    // 处理在段落中的回车（防止嵌套）
    handleEnterInParagraph(range) {
      // 创建新段落
      const newParagraph = document.createElement('p')
      newParagraph.className = 'halo-paragraph'
      newParagraph.innerHTML = '<br>'

      // 找到当前段落
      const currentParagraph = this.getCurrentParagraph(range)
      if (currentParagraph) {
        // 在当前段落后插入新段落
        currentParagraph.parentNode.insertBefore(newParagraph, currentParagraph.nextSibling)

        // 将光标移动到新段落
        const newRange = document.createRange()
        newRange.setStart(newParagraph, 0)
        newRange.collapse(true)

        const selection = window.getSelection()
        selection.removeAllRanges()
        selection.addRange(newRange)

        this.updateData(true)
      }
    },

    // 处理包含话题的段落中的回车
    handleEnterInTopicParagraph(range, currentParagraph) {
      // 创建新段落
      const newParagraph = document.createElement('p')
      newParagraph.className = 'halo-paragraph'

      // 提取光标后的内容
      const afterContent = this.extractContentAfterCursor(range, currentParagraph)

      if (afterContent && afterContent.childNodes.length > 0) {
        while (afterContent.firstChild) {
          newParagraph.appendChild(afterContent.firstChild)
        }
      } else {
        newParagraph.innerHTML = '<br>'
      }

      // 插入新段落
      currentParagraph.parentNode.insertBefore(newParagraph, currentParagraph.nextSibling)

      // 将光标移动到新段落开始
      const newRange = document.createRange()
      if (newParagraph.firstChild && newParagraph.firstChild.nodeType === Node.TEXT_NODE) {
        newRange.setStart(newParagraph.firstChild, 0)
      } else {
        newRange.setStart(newParagraph, 0)
      }
      newRange.collapse(true)

      const selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(newRange)

      this.updateData(true)
    },

    // 提取光标后的内容
    extractContentAfterCursor(range, paragraph) {
      console.log('🔍 [TOPIC-FIX] extractContentAfterCursor called')
      const fragment = document.createDocumentFragment()
      const container = range.startContainer
      const offset = range.startOffset

      console.log('🔍 [TOPIC-FIX] Container:', container, 'Offset:', offset)

      // 检查是否在话题空格文本节点内部
      let topicSpaceNode = null
      if (container.nodeType === Node.TEXT_NODE &&
          container.textContent === '\u00A0' &&
          container.previousSibling &&
          container.previousSibling.tagName === 'MDD-TOPIC') {
        topicSpaceNode = container
        console.log('🔍 [TOPIC-FIX] Found topic space node:', topicSpaceNode)
      }

      // 保存段落的最后一个子节点，避免在提取过程中发生变化
      const paragraphLastChild = paragraph.lastChild

      // 创建一个临时range来选择光标后的所有内容
      const extractRange = document.createRange()

      if (topicSpaceNode) {
        // 如果在话题空格文本节点内部，从节点后面开始提取
        console.log('🔍 [TOPIC-FIX] Extracting from after topic space node')
        if (topicSpaceNode.nextSibling) {
          extractRange.setStartBefore(topicSpaceNode.nextSibling)
        } else {
          return fragment // 没有后续内容
        }
      } else if (container.nodeType === Node.TEXT_NODE) {
        // 如果在文本节点中，从当前位置开始
        if (offset < container.textContent.length) {
          // 分割当前文本节点
          const afterText = container.textContent.slice(offset)
          container.textContent = container.textContent.slice(0, offset)

          // 创建新的文本节点包含后面的文本
          if (afterText) {
            const newTextNode = document.createTextNode(afterText)
            container.parentNode.insertBefore(newTextNode, container.nextSibling)
            extractRange.setStartBefore(newTextNode)
          } else {
            // 如果没有剩余文本，从下一个兄弟节点开始
            if (container.nextSibling) {
              extractRange.setStartBefore(container.nextSibling)
            } else {
              return fragment // 没有后续内容
            }
          }
        } else {
          // 光标在文本节点末尾，从下一个兄弟节点开始
          if (container.nextSibling) {
            extractRange.setStartBefore(container.nextSibling)
          } else {
            return fragment // 没有后续内容
          }
        }
      } else if (container.nodeType === Node.ELEMENT_NODE) {
        // 如果在元素节点中，从指定位置的子节点开始
        if (offset < container.childNodes.length) {
          extractRange.setStartBefore(container.childNodes[offset])
        } else {
          return fragment // 没有后续内容
        }
      } else {
        return fragment // 其他情况返回空fragment
      }

      // 设置提取范围的结束位置为段落的末尾（使用保存的最后子节点）
      if (paragraphLastChild) {
        extractRange.setEndAfter(paragraphLastChild)
      } else {
        return fragment
      }

      // 提取内容到fragment
      try {
        const extractedContent = extractRange.extractContents()
        fragment.appendChild(extractedContent)
        console.log('🔍 [TOPIC-FIX] Extracted content:', fragment)
      } catch (e) {
        console.warn('提取内容时出错:', e)
      }

      return fragment
    },

    // 安全删除处理（防止嵌套段落）
    handleSafeDeleteInTopicParagraph(range, deleteKey) {
      const container = range.startContainer
      const offset = range.startOffset

      // 如果是在文本节点中删除
      if (container.nodeType === Node.TEXT_NODE) {
        if (deleteKey === 'Backspace' && offset > 0) {
          // 删除前一个字符
          const newText = container.textContent.slice(0, offset - 1) + container.textContent.slice(offset)
          container.textContent = newText

          // 设置新的光标位置
          const newRange = document.createRange()
          newRange.setStart(container, offset - 1)
          newRange.collapse(true)

          const selection = window.getSelection()
          selection.removeAllRanges()
          selection.addRange(newRange)

          this.updateData(true)
          return true
        } else if (deleteKey === 'Delete' && offset < container.textContent.length) {
          // 删除后一个字符
          const newText = container.textContent.slice(0, offset) + container.textContent.slice(offset + 1)
          container.textContent = newText

          // 光标位置保持不变
          const newRange = document.createRange()
          newRange.setStart(container, offset)
          newRange.collapse(true)

          const selection = window.getSelection()
          selection.removeAllRanges()
          selection.addRange(newRange)

          this.updateData(true)
          return true
        }
      }

      return false
    },

    genIconDom(props) {
      const frag = document.createDocumentFragment()
      const vnode = h(CircleCloseFilled, props)
      render(vnode, frag)
      return frag
    },
    handleImgDelete() {
      this.updateData(true)
    },
    clearFormat() {
      this.editor.removeAllFormatting()
      this.updateData(true)
    },
    setCursor() {
      this.cursorStyle = this.cursorStyle === cursorImg ? 'auto' : cursorImg
      console.log(this.styleStatus, 999)
      this.curStyle = { ...this.styleStatus }
      this.editorDom.addEventListener('mouseup', this.handleCopyFormatUp)
    },
    handleCopyFormatUp() {
      const selection = this.editor.getSelection()
      if (!selection.collapsed) {
        this.editor.removeAllFormatting()
        Object.keys(this.curStyle).forEach((key) => {
          if (this.curStyle[key]) {
            if (key === 'size' && this.curStyle[key] === 17) return
            const method = keyToEditorMethod[key]
            method &&
              this.editor[method](
                key === 'size' || key === 'color'
                  ? this.curStyle[key]
                  : undefined
              )
          }
        })
      }
      this.cursorStyle = 'auto'
      this.editorDom.removeEventListener('mouseup', this.handleCopyFormatUp)
    },
    updateData(flag) {
      const { contentLen, imgCount, titleCount } = this.getEditorData()
      this.contentLen = contentLen
      this.imgCount = imgCount
      this.titleCount = titleCount
      this.backState = this.editor.getUndoOrRedoState()
      this.$emit('backData', flag, contentLen, imgCount)
    },
    showUploadVideo() {
      this.visibleVideo = true
    },
    uploadVideo(e) {
      const files = e.target.files
      this.visibleVideo = false
      const fileName = ((files[0] && files[0].name) || '').toLowerCase()
      if (files[0].size > 1 * 1024 * 1024 * 1024) {
        return me.setToast('视频大小不能超过1GB')
      }
      if (fileName.indexOf('.mp4') === -1 && fileName.indexOf('.mov') === -1) {
        return me.setToast('视频格式不是mp4或mov')
      }
      const me = this
      if (me.loading) return
      me.closeDialog()
      me.loading = true
      this.editor['insertVideoProgress']()
      this.$emit(
        'insertVideo',
        this,
        files,
        (result) => {
          const video = {
            duration: result.duration || '',
            link: result.videoUrl || '',
            img: me.coverUrl || result.coverUrl || result.customCover || '',
            id: result.videoId,
            desc: '',
            vodSize: result.vodSize || '', // 视频大小
            vodType: result.vodType || '', // 图片尺寸
          }
          me.$emit('updateAddVideo', true)
          me.editor['insertVideo'](result.videoUrl, video)
          me.updateData(true)
        },
        (_) => {
          me.setMessageBoxNoCancel(_)
        },
        () => {
          me.loading = false
          me.loadingText = ''
          me.removeProgress()
        }
      )
    },
    replaceImg(e) {
      this.$emit('insertImgs', e.target.files, 'replace', (type, src) => {
        this.visibleImg = false
        this.imgUploadHandler(type, src)
      })
    },
    replacePoster(e) {
      this.$emit(
        'insertImgs',
        e.target.files,
        'replace-poster',
        (type, src) => {
          this.visibleImg = false
          this.imgUploadHandler(type, src)
        }
      )
    },
    againImg(e) {
      this.$emit(
        'insertImgs',
        e.target.files,
        'uploadAgainImages',
        (type, src) => {
          this.visibleImg = false
          this.imgUploadHandler(type, src)
        }
      )
    },
    handleClose() {
      this.linkForm.linkAddress = ''
      this.linkForm.linkWriting = ''
      this.viewLinkDialog = false
    },
    showLink() {
      this.viewLinkDialog = true
    },
    insertLink() {
      if (!this.linkForm.linkAddress) return ElMessage.error('请输入链接地址')
      if (!this.linkForm.linkWriting) return ElMessage.error('请输入链接文案')
      if (!this.validUrl(this.linkForm.linkAddress)) {
        return this.setToast('URL无效')
      }
      // this.editor.makeLink(this.linkForm.linkWriting, {
      //   href: this.linkForm.linkAddress
      // })
      this.editor.insertLink(
        this.linkForm.linkAddress,
        this.linkForm.linkWriting
      )
      this.updateData(true)
      this.viewLinkDialog = false
    },
    // mark 
    updateTopicPosition() {
      const paragraphList =
        document.querySelectorAll('#editor-content .halo-paragraph') || []
      Array.from(paragraphList).forEach((paragraph) => {
        const topicList = paragraph.querySelectorAll('mdd-topic') || []
        Array.from(topicList).forEach((topic) => {
          const topicText = topic.innerText
          const topicLength = topicText.length
          const topicIndex = paragraph.innerText.indexOf(topicText)
          const itemData = JSON.parse(topic.getAttribute('data-topic') || '{}')
          topic.setAttribute(
            'data-topic',
            JSON.stringify({
              ...itemData,
              startIndex: topicIndex,
              endIndex: topicIndex + topicLength,
            })
          )
        })
      })
    },
    getHtml(type) {
      this.updateTopicPosition()
      const html = this.editor.getHTML()
      const data = this.filterHtml(html, type)
      return data
    },
    generateImgHtml(img, isGif, desc) {
      let descHtml = ''
      if (desc) {
        descHtml = ` <div
                    class="content-explain"
                  >
                    <img src="../../assets/img/<EMAIL>" alt />
                    <p>${desc}</p> 
                  </div>`
      }
      return `
      <div class="show-img-content">
                    <img
                      alt
                      src="${img}"
                      class="detail-imgs"
                      onerror="onerror=null;src='/static/img/web_img_zhanwei.png'"
                    />
                    ${isGif ? '<div  class="is-gif">GIF</div>' : ''}
                  </div>
                 ${descHtml}
      `
    },
    generateLinkHtml(url, text) {
      return `
      <div class="url-mes">
                    <i></i>
                    <p>
                      <span :href="${url}">${text}</span>
                    </p>
                  </div>
      `
    },
    filterHtml(html, type) {
      const div = document.createElement('div')
      div.innerHTML = html
      const nodes = [...div.childNodes]
      nodes.forEach((node) => {
        if (node.nodeType === 1) {
          if (node.classList.contains('halo-img-content')) {
            const children = [...node.childNodes]
            const img = node.querySelector('.halo-picture-area')
            const isGif = img.src.indexOf('.gif!nowater') > -1
            if (isGif && type === 'preview') {
              img.src = img.src.replace('.gif!nowater', '.gif')
            }
            if (type === 'preview') {
              node.innerHTML = this.generateImgHtml(
                img.src,
                isGif,
                img.dataset.desc
              )
              return
            }
            children.forEach((child) => {
              if (
                child.nodeType === 1 &&
                ((child.tagName !== 'svg' &&
                  !child.className.includes('halo')) ||
                  child.tagName === 'svg')
              ) {
                const parent = child.parentNode
                parent.removeChild(child)
              }
            })
          } else if (node.classList.contains('article-wrap')) {
            if (type === 'preview') return
            const id = JSON.parse(node.dataset.article).id
            const parent = node.parentNode
            const tag = document.createElement('halo-article')
            tag.dataset.article = JSON.stringify({ id })
            parent.replaceChild(tag, node)
          } else if (node.classList.contains('halo-video-content')) {
            node.removeChild(node.querySelector('.video-delete'))
          } else if (node.classList.contains('halo-link')) {
            if (type === 'preview') {
              const a = node.querySelector('.halo-link-mes')

              node.innerHTML = this.generateLinkHtml(a.dataset.url, a.innerText)
              return
            }
            const img = node.querySelector('.link-img')
            const del = node.querySelector('.img-delete')
            node.removeChild(img)
            node.removeChild(del)
          }
        }
      })
      return div.innerHTML
    },
    setHtml() {
      this.initData()
    },
    async parseHtml(html, essayPicRelVOList) {
      const me = this
      const div = document.createElement('div')
      div.innerHTML = html
      const frag = document.createDocumentFragment()
      frag.appendChild(this.editor.empty(div))
      const nodes = [...frag.childNodes]

      // 处理所有mdd-topic元素，确保它们不可编辑
      const mddTopics = frag.querySelectorAll('mdd-topic')
      mddTopics.forEach(topic => topic.setAttribute('contenteditable', 'false'))

      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        if (node.nodeType === 1) {
          if (node.tagName.toLowerCase() === 'halo-article') {
            await this.parseArticle(node)
          } else if (node.tagName.toLowerCase() === 'halo-good') {
            this.parseGood(node)
          } else if (node.classList.contains('halo-img-content') || node.classList.contains('halo-modify-content')) {
            const img = node.querySelector('img') || node.querySelector('modify')
            node.setAttribute('tabindex', -1)
            node.setAttribute('draggable', true)
            node.setAttribute('contenteditable', false)
            img.setAttribute('draggable', false)
            this.setListener(node)
            const desc = img.dataset.desc
            img.style.margin = '0'
            const seamlessFlag =
              JSON.parse(img.getAttribute('data'))?.seamlessFlag || '0'
            const paintCopyable =
              JSON.parse(img.getAttribute('data'))?.paintCopyable || '0'
            const id =
              JSON.parse(img.getAttribute('data'))?.id || ''
            // console.log('type >>>>',this.imgType, JSON.parse(img.getAttribute('data')),img.getAttribute('data'))
            if (essayPicRelVOList) {
              const arr = img.src.split('/')
              const id = arr[arr.length - 1]
              let url = essayPicRelVOList.find(
                (obj) => obj.id === String(id)
              )?.url
              if(url) {
                img.src = url
              }
            }
            this.insertImgOperateBtns(
              this.editor,
              {
                content: desc,
                seamlessFlag,
                paintCopyable,
                id
              },
              nodes.length === i
            ).forEach((child) => {
              node.appendChild(child)
            })
          } else if (node.classList.contains('halo-video-content')) {
            this.parseVideo(node, essayPicRelVOList)
          } else if (node.classList.contains('halo-link')) {
            this.parseLink(node)
          }
        }
      }
      return frag
    },
    parseLink(node) {
      const img = this.editor.createElement('img', {
        class: 'link-img',
        src: '/static/img/<EMAIL>',
      })
      const del = this.generateDelLinkIcon()
      node.prepend(img)
      node.appendChild(del)
    },
    parseVideo(node, essayPicRelVOList) {
      const me = this
      const videoData = JSON.parse(node.dataset.video)

      const video = node.querySelector('.halo-video-area')
      const videoId = video.src
      const arr = videoId.split('/')
      const id = arr[arr.length - 1]
      video.src = essayPicRelVOList.find((obj) => obj.id === String(id)).url
      videoData.link = video.src
      if (videoData.img) {
        const poster = essayPicRelVOList.find(
          (obj) => obj.id === videoData.img
        ).url
        video.poster = poster
        videoData.img = poster
      }
      node.setAttribute('data-video', JSON.stringify(videoData))
      var delBtn = this.genIconDom({
        class: 'pointer video-delete  icon',
        contenteditable: 'false',
        onClick(e) {
          me.removeParentByClass(e.target, 'halo-video-content')
          e.stopPropagation()
        },
      })
      // node.onclick = function() {
      //   me.setToast('不支持播放');
      // }
      const replaceBtn = me.insertVideoBtns(me.editor, video)
      node.appendChild(delBtn)
      node.appendChild(replaceBtn)
      node.setAttribute('tabindex', -1)
      node.setAttribute('draggable', true)

      me.setListener(node)
    },
    async parseArticle(node) {
      const data = JSON.parse(node.dataset.article)
      await this.getEassyDetail(data.id, (info) => {
        const el = mountArticleDom(
          {
            article: info,
          },
          () => {
            this.updateData(true)
          }
        )
        const parent = node.parentNode
        parent.replaceChild(el, node)
      })
    },
    parseGood() {},
    setStyle(type) {
      this.editor[type]()
      this.updateData(true)
    },
    getCursorAlignStyle() {
      const selection = this.editor._lastSelection
      let activeAlign = ''
      if (selection) {
        let currentParagraph = selection.startContainer
        if (currentParagraph.tagName !== 'P') {
          currentParagraph = currentParagraph.parentNode
        }
        Array.from(currentParagraph.classList).forEach((item) => {
          if (item.includes('align')) {
            activeAlign = item.split('-')[1]
          }
        })
      }
      return activeAlign
    },
    getCursorFontsize() {
      const selection = this.editor._lastSelection
      let activeAlign = ''
      if (selection) {
        let currentParagraph = selection.startContainer
        if (currentParagraph.tagName !== 'P') {
          currentParagraph = currentParagraph.parentNode
        }
        Array.from(currentParagraph.classList).forEach((item) => {
          if (item.includes('align')) {
            activeAlign = item.split('-')[1]
          }
        })
      }
      return activeAlign
    },
    removeParentByClass(child, classname) {
      while (child) {
        if (child.classList.contains(classname)) {
          child.parentNode.removeChild(child)
          break
        }
        child = child.parentNode
      }
    },
    findParentByClass(child, classname) {
      while (child) {
        if (child.classList.contains(classname)) {
          return child
        }
        child = child.parentNode
      }
    },
    selectArticle(item) {
      if (item.isVideo) {
        const data = item?.mediaInfo[0] || {}
        this.editor.insertVideo(data.link, {
          link: data.link,
          img: data.img,
          id: data.id,
        })
        this.visibleArticle = false
        this.updateData(true)
        return
      }
      const el = mountArticleDom(
        {
          article: item,
        },
        () => {
          this.updateData(true)
        }
      )
      this.editor['insertArticle'](el)
      this.article = item
      this.visibleArticle = false
      this.hasArticleCard = true
      this.updateData(true)
    },
    editProgress() {},
    removeProgress() {
      const box = document.querySelector('.progress-wrap')
      const parentNode = box.parentNode
      parentNode.removeChild(box)
    },
    collectArticle() {
      this.visibleCollectArticle = true
    },
    uploadArticle() {
      this.visibleArticle = true
    },
    uploadImg(type, num) {
      this.visibleImg = true
      this.imgType = type
      this.imgList = []
      this.imgNum = num || 20
    },
    async submitImg() {
      this.count = 0
      this.countList = []
      this.seamlessCount = 0
      if (this.imgList.length) {
        const idList = []
        const list_1 = []
        const list_2 = []
        const list_3 = []
        this.imgList.forEach((v) => {
          idList.push(v.id)
          if (v.type === 1) {
            list_1.push(v)
          } else if (v.type === 3) {
            list_3.push(v)
          } else {
            list_2.push(v)
          }
        })
        const list_2_new = await this.reviseFilesImg(list_2)
        const list_3_new = await this.reviseSrcImg(list_3)
        const list_all = [...list_1, ...list_2_new, ...list_3_new]
        const imgList_new = []
        idList.forEach((id) => {
          const item = list_all.find((value) => value.id === id)
          if (item && item.src) {
            imgList_new.push(item)
          }
        })
        if (this.imgType === 'coverImg') {
          this.$emit('insertImgs', imgList_new, this.imgType)
        } else {
          imgList_new.forEach((img) => {
            this.imgUploadHandler('img', img.src)
          })
        }
        this.visibleImg = false
      } else {
        this.visibleImg = false
      }
    },
    async reviseFilesImg(list) {
      if (list.length) {
        const p_l = []
        list.forEach((v) => {
          const p = new Promise((resolve, reject) => {
            try {
              this.$emit('insertImgs', [v.file], 'img', (type, src) => {
                resolve({ ...v, src })
              })
            } catch (error) {
              reject(error)
            }
          })
          p_l.push(p)
        })
        const res = await Promise.all(p_l)
        return res
      } else {
        return []
      }
    },
    async reviseSrcImg(list) {
      const copyToEssayImg = this.request.copyToEssayImg
      if (copyToEssayImg && list.length) {
        const arr = []
        list.forEach((v) => {
          arr.push(v.src)
        })
        const res = await copyToEssayImg({ imgUrls: arr.join(',') })
        if (res.data.code === 0) {
          const data = res.data.data || ''
          const list_new = data ? data.split(',') : []
          list.forEach((v, i) => {
            v.src = list_new[i] || ''
          })
          return list
        } else {
          return []
        }
      } else {
        return []
      }
    },
    imgUploadHandler(type, imgSrc) {
      const me = this
      if (type && imgSrc) {
        // const id = uuidv4()
        // me.imgList.push({ id, src: imgSrc })
        if (type === 'uploadAgainImages') {
          // console.log('uploadAgainImages', me.currentImg, imgSrc);
          this.updateImage(imgSrc, this.currentImg)
          this.currentImg = ''
        } else {
          if (type === 'replace') {
            this.removePreviousImg && this.removePreviousImg()
            this.editor['insertImages'](
              imgSrc,
              {
                src: imgSrc,
                img: imgSrc,
                selected: false,
                seamlessFlag: me.replaceSeamless,
                type: '2',
              },
              this.range
            )
          } else if (type === 'replace-poster') {
            if (this.currentVideo) {
              this.currentVideo.poster = imgSrc
              try {
                const videoData = JSON.parse(
                  this.currentVideo.getAttribute('data') || '{}'
                )
                this.currentVideo.setAttribute(
                  'data',
                  JSON.stringify({ ...videoData, img: imgSrc })
                )
                const contentNode = this.currentVideo.parentNode
                const contentData = JSON.parse(
                  contentNode.dataset.video || '{}'
                )
                contentNode.dataset.video = JSON.stringify({
                  ...contentData,
                  img: imgSrc,
                })
              } catch (error) {
                console.log(error)
              }
              this.currentVideo = null
            }
          } else {
            this.seamlessCount++
            const isLast = this.seamlessCount === this.imgList.length
            const isSeameless = this.imgType === 'seamless' && !isLast
            this.editor['insertImages'](imgSrc, {
              src: imgSrc,
              img: imgSrc,
              selected: false,
              seamlessFlag: isSeameless ? '1' : '0',
              type: '2',
            })
          }
        }
        this.updateData(true)
      } else {
        this.setToast(rst.origin.name + '上传失败，请重试')
      }
    },

    insertVideoBtns(editor, node) {
      const me = this
      var replaceBtn = editor.createElement('div', {
        class: 'video-cover-replace pointer',
        contenteditable: 'false',
      })
      replaceBtn.onclick = function (e) {
        const posterBtn = document.querySelector('.replace-poster')
        posterBtn.click()
        me.currentVideo = node
        e.stopPropagation()
      }
      replaceBtn.innerText = '更换封面'
      return replaceBtn
    },

    insertImgOperateBtns(editor, data) {
      const me = this
      var replaceBtn = editor.createElement('div', {
        class: 'img-replace pointer',
        contenteditable: 'false',
      })
      var delBtn = this.genIconDom({
        class: 'img-delete pointer icon',
        contenteditable: 'false',
        onClick(e) {
          if (me.disabled) return
          me.removeSeamlessLaster(e)
          me.removeParentByClass(e.target, 'halo-img-content')
          me.updateData(true)
        },
      })
      var descInputWrap
      // console.log(data.seamlessFlag, 'count<<<<<<')
      // if(data.seamlessFlag !== '1') {
      var descInput = editor.createElement('textarea', {
        class: 'desc-input',
        maxlength: '50',
        rows: '2',
        cols: '50',
        placeholder: '请输入图片描述(最多50字)',
        contenteditable: 'false',
      })
      descInput.disabled = me.disabled
      descInput.oninput = function (e) {
        const parent = me.findParentByClass(e.target, 'halo-img-content')
        const img = parent.querySelector('.halo-picture-area')
        img.dataset.desc = e.target.value
        if (e.target.value.length > 49) {
          return me.setToast('限制50个字符')
        }
      }
      descInput.value = data.content || ''
      descInputWrap = editor.createElement(
        'div',
        {
          class: 'desc-input-wrap',
          contenteditable: 'false',
          style: `${data.seamlessFlag === '1' && 'display: none'}`,
        },
        [descInput]
      )
      // }

      replaceBtn.onclick = function (e) {
        if (me.disabled) return
        // me.removeParentByClass(e.target, 'img-content')
        const current = me.findParentByClass(e.target, 'halo-img-content')
        const range = document.createRange()
        range.setEnd(current.previousElementSibling, 1)
        range.collapse(false)
        me.range = range
        me.replaceSeamless = data.seamlessFlag || '0'
        const replaceInput = document.querySelector('.replace-input')
        me.removePreviousImg = () =>
          me.removeParentByClass(e.target, 'halo-img-content')

        replaceInput.click()
      }
      delBtn.onclick = function (e) {
        if (me.disabled) return
        //移除的是无缝图最后一个
        const cur = me.findParentByClass(e.target, 'halo-img-content')
        const currentImg = cur.getElementsByClassName('halo-picture-area')[0]
        const currentData = JSON.parse(currentImg?.getAttribute('data') || '{}')
        if (currentData.seamlessFlag === '0') {
          if (!cur.previousElementSibling) return
          try {
            const imgcont = cur.previousElementSibling.previousElementSibling
            const input = imgcont.getElementsByClassName('desc-input-wrap')[0]
            const pic = imgcont.getElementsByClassName('halo-picture-area')[0]
            const data = JSON.parse(pic.getAttribute('data'))
            data.seamlessFlag = '0'
            pic.setAttribute('data', JSON.stringify(data))
            input.style.display = ''
          } catch (e) {
            console.log(e)
          }
        }
        me.removeParentByClass(e.target, 'halo-img-content')
        me.updateData(true)
      }
      replaceBtn.innerHTML = '替换'
      return [replaceBtn, delBtn, descInputWrap]
    },
    removeSeamlessLaster(e) {
      //移除的是无缝图最后一个
      const me = this
      const cur = me.findParentByClass(e.target, 'halo-img-content')
      const currentImg = cur.getElementsByClassName('halo-picture-area')[0]
      const currentData = JSON.parse(currentImg?.getAttribute('data') || '{}')
      if (currentData.seamlessFlag === '0') {
        if (!cur.previousElementSibling) return
        try {
          const imgcont = cur.previousElementSibling.previousElementSibling
          const input = imgcont.getElementsByClassName('desc-input-wrap')[0]
          const pic = imgcont.getElementsByClassName('halo-picture-area')[0]
          const data = JSON.parse(pic?.getAttribute('data') || '{}')
          data.seamlessFlag = '0'
          pic.setAttribute('data', JSON.stringify(data))
          input.style.display = ''
        } catch (e) {
          console.log(e)
        }
      }
    },
    // 初始化编辑器数据 displayData
    initData(data = '', essayPicRelVOList) {
      // 模拟数据
      // const html = `
      //   <p class="halo-paragraph">23<b>42342</b>34<br></p>
      //   <p class="halo-paragraph">23<b>42342</b>34<br></p>
      //   <p class="halo-paragraph">23<b>42342</b>34<br></p>
      //   <p class="halo-paragraph">23<b>42342</b>34<br></p>
      //   <p class="halo-paragraph"><br></p>
      //   `
      const html = `
        <h2 class="halo-paragraph-title">rwerwerwer<br></h2>
        <p class="halo-paragraph">23<b>42342</b>34<br></p>
        <p class="halo-paragraph">地表最聪明的<mdd-topic data-topic={"topicId":"12345","topicType":0,"startIndex":"12","endIndex":"16"}>#123</mdd-topic> 动力心脏</p>
        <ol>
          <li>233342<br></li>
          <li>23423<br></li>
        </ol>
        <p class="halo-paragraph"><br></p>
        <div class="halo-img-content" contenteditable="false"><img id=""
            src="http://imgs2.58moto.com/forum/20220909/3e44260d22404af8b3014487c9142871.jpeg!nowater?_516_1015"
            class="halo-picture-area"
            data="{&quot;src&quot;:&quot;http://imgs2.58moto.com/forum/20220909/3e44260d22404af8b3014487c9142871.jpeg!nowater?_516_1015&quot;,&quot;img&quot;:&quot;http://imgs2.58moto.com/forum/20220909/3e44260d22404af8b3014487c9142871.jpeg!nowater?_516_1015&quot;,&quot;selected&quot;:false,&quot;type&quot;:&quot;2&quot;}"
            data-content="" contenteditable="false" data-desc="232323"></div>
        <p class="halo-paragraph"><br></p>
        <p class="halo-paragraph"><br></p>
        <halo-article data-article="{&quot;id&quot;:5675033}"></halo-article>
        <p class="halo-paragraph"><br></p>
        <p class="halo-paragraph"><br></p>
        <div class="halo-link" contenteditable="false"><a class="halo-link-mes" data-url="www.baidu.com" target="_blank">2323</a></div>
        <p class="halo-paragraph"><br></p>
        `
      data = html

      this.parseHtml(data, essayPicRelVOList).then((res) => {
        // 无法使用innerhtml，会导致绑定事件失效
        this.editorDom.innerHTML = ''
        this.editor.insertElement(res)
        this.updateData()
      })
    },
    // 设置富文本组件
    initSquire() {
      const me = this
      const editorDom = me.editorDom
      // Squire 自定义 paragraph 段落标签、paragraph-title 段落标题
      me.editor = new window.Squire(me.editorDom, {
        blockTag: 'p',
        blockAttributes: {
          class: 'halo-paragraph',
        },
      })

      // event start
      editorDom.ondragover = function (e) {
        e.preventDefault()
        if (
          me.targetMove &&
          me.moverClasses.includes(me.targetMove.className)
        ) {
          if (e.target.offsetParent === editorDom) {
            me.overLine = e.target.offsetHeight + e.target.offsetTop + 'px'
            me.lasteDropDom = e.target
          }
        }
        const bottom = document.body.clientHeight - e.clientY - 280 // 获取鼠标距离底部的距离
        // 此时向上滚动 距离顶部距离
        if (e.clientY < 310) {
          scrollBy({
            top: -20,
            left: 0,
            behavior: 'smooth',
          })
        } else if (bottom < 100) {
          // 此时向下滚动
          scrollBy({
            top: 20,
            left: 0,
            behavior: 'smooth',
          })
        }
      }
      // 这个不加会导致拖拽图片过快 ondrop 方法不会触发
      // ondragover 里的代码放进这里也可以，但是滚动会不顺畅
      editorDom.ondragenter = function (e) {
        e.preventDefault()
      }
      editorDom.ondrop = function (e) {
        e.preventDefault()
        if (
          me.targetMove &&
          me.moverClasses.includes(me.targetMove.className)
        ) {
          const target = me.lasteDropDom || e.target
          if (target.parentNode !== editorDom) {
            return
          }
          if (target === editorDom) {
            target.appendChild(me.targetMove)
          } else {
            target.after(me.targetMove)
          }
          me.targetMove = ''
          me.lasteDropDom = ''
        }
        me.overLine = ''
      }
      this.editor.addEventListener('pathChange', (obj) => {
        this.fontInfo = this.editor.getFontInfo()
        this.styleStatus.bold = this.editor.hasFormatTag('B')
        this.styleStatus.italic = this.editor.hasFormatTag('I')
        this.styleStatus.underline = this.editor.hasFormatTag('U')
        this.styleStatus.title = this.editor.hasFormatTag('H2')
        this.styleStatus.size =
          parseInt(this.fontInfo.size) === 100 ? 17 : this.fontInfo.size
        this.styleStatus.color = this.fontInfo.color
      })
      me.editor.addEventListener('dragover', (event) => {
        event.preventDefault()
      })
      me.editor.addEventListener('drop', (event) => {
        const clipboardData = event.dataTransfer || {}
        const files = clipboardData.files
        if (files && files.length) {
          files.forEach((file) => {
            if (file.type.includes('image')) {
              me.$emit('insertImgs', [file], 'img', (type, src) => {
                me.visibleImg = false
                me.imgUploadHandler(type, src)
              })
            }
            if (file.type.includes('video')) {
              me.uploadVideo({
                target: {
                  files: [file],
                },
              })
            }
          })
        }
        event.preventDefault()
      })
      // 添加点击事件监听器，防止光标进入mdd-topic元素
      me.editorDom.addEventListener('click', function(event) {
        const target = event.target
        if (target && target.tagName === 'MDD-TOPIC') {
          event.preventDefault()
          // 将光标移动到mdd-topic元素后面
          const selection = window.getSelection()
          const range = document.createRange()
          range.setStartAfter(target)
          range.collapse(true)
          selection.removeAllRanges()
          selection.addRange(range)
        }
      })

      // 添加输入事件监听器，处理在mdd-topic后面的输入
      me.editorDom.addEventListener('input', function(event) {
        // 延迟处理，确保DOM更新完成
        setTimeout(() => {
          const selection = window.getSelection()
          if (selection.rangeCount === 0) return

          const range = selection.getRangeAt(0)
          const container = range.startContainer

          // 获取当前段落
          let paragraph = container
          while (paragraph && paragraph.nodeType !== Node.ELEMENT_NODE) {
            paragraph = paragraph.parentNode
          }
          while (paragraph && !paragraph.classList?.contains('halo-paragraph')) {
            paragraph = paragraph.parentNode
          }

          if (paragraph) {
            me.checkAndTriggerSearch(paragraph)
          }
        }, 10)
      })

      me.editor.addEventListener('keydown', function (event) {
        console.log('🔍 [KEYDOWN] Key pressed:', event.key, 'Meta:', event.metaKey, 'Ctrl:', event.ctrlKey, 'Alt:', event.altKey, 'Shift:', event.shiftKey)

        // 话题弹框逻辑
        me.handleTopicInput(event)

        // 处理回车键逻辑
        if (event.key === 'Enter') {
          console.log('🔍 [ENTER] Enter key pressed')
          const selection = window.getSelection()
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            const startContainer = range.startContainer

            // 检查是否在mdd-topic元素内部
            let isInMddTopic = false
            let mddTopicElement = null

            if (
              startContainer.nodeType === Node.ELEMENT_NODE &&
              startContainer.tagName === 'MDD-TOPIC'
            ) {
              isInMddTopic = true
              mddTopicElement = startContainer
            } else if (startContainer.nodeType === Node.TEXT_NODE) {
              const parent = startContainer.parentNode
              if (parent && parent.tagName === 'MDD-TOPIC') {
                isInMddTopic = true
                mddTopicElement = parent
              }
            }

            // 如果在mdd-topic内部，将光标移动到topic后面再处理回车
            if (isInMddTopic && mddTopicElement) {
              event.preventDefault()

              // 将光标移动到mdd-topic元素后面
              const newRange = document.createRange()
              newRange.setStartAfter(mddTopicElement)
              newRange.collapse(true)
              selection.removeAllRanges()
              selection.addRange(newRange)

              // 手动触发回车逻辑，创建新段落
              me.handleEnterKeyInParagraphWithTopic(newRange)
              return
            }

            // 检查是否在两个连续的mdd-topic之间或话题前面
            if (me.isBetweenTwoTopics(range) || me.isBeforeTopic(range)) {
              event.preventDefault()
              me.handleEnterBetweenTopics(range)
              return
            }

            // 检查当前段落是否包含mdd-topic元素或者是否会创建嵌套段落
            let currentParagraph = startContainer
            while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
              currentParagraph = currentParagraph.parentNode
            }
            while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
              currentParagraph = currentParagraph.parentNode
            }

            // 检查是否会创建嵌套的halo-paragraph
            if (me.wouldCreateNestedParagraph(range)) {
              event.preventDefault()
              me.handleEnterKeyInParagraphWithTopic(range)
              return
            }

            if (currentParagraph && currentParagraph.querySelector('mdd-topic')) {
              // 当前段落包含话题，确保能正常创建新段落
              event.preventDefault()
              me.handleEnterKeyInParagraphWithTopic(range)
              return
            }
          }
        }

        // 按下了退格键或删除键（fn+delete在Mac上会触发Delete键事件）
        if (['Backspace', 'Delete'].includes(event.key)) {
          console.log('🔍 [DELETE] Delete key pressed:', event.key, 'Meta:', event.metaKey, 'Ctrl:', event.ctrlKey)

          // 首先检查是否会导致嵌套段落问题
          const selection = window.getSelection()
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            const startContainer = range.startContainer

            // 找到当前段落
            let currentParagraph = startContainer
            while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
              currentParagraph = currentParagraph.parentNode
            }
            while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
              currentParagraph = currentParagraph.parentNode
            }

            if (currentParagraph) {
              const paragraphContent = currentParagraph.innerHTML.trim()
              console.log('🔍 Current paragraph content:', paragraphContent)
              console.log('🔍 Current paragraph HTML:', currentParagraph.outerHTML)
              console.log('🔍 Editor total paragraphs before:', me.editorDom.querySelectorAll('.halo-paragraph').length)

              // 添加延迟检查，看看段落数量是否发生变化
              setTimeout(() => {
                const paragraphsAfter = me.editorDom.querySelectorAll('.halo-paragraph').length
                console.log('🔍 Editor total paragraphs after (100ms):', paragraphsAfter)
                if (paragraphsAfter > me.editorDom.querySelectorAll('.halo-paragraph').length) {
                  console.log('🚨 PARAGRAPH INJECTION DETECTED!')
                }
              }, 100)
            }

            // 检查删除操作是否会触发重新渲染导致嵌套段落
            if (me.wouldDeleteCauseNestedParagraph(range, event.key)) {
              console.log('🔍 wouldDeleteCauseNestedParagraph returned true')

              // 对于空段落，直接阻止默认行为并删除段落
              if (currentParagraph) {
                const paragraphContent = currentParagraph.innerHTML.trim()
                if (paragraphContent === '<br>' || paragraphContent === '') {
                  console.log('🚫 Directly preventing empty paragraph delete and removing it')

                  // 如果不是最后一个段落，直接删除
                  if (currentParagraph.parentNode && currentParagraph.parentNode.children.length > 1) {
                    // 设置光标到前一个段落的末尾
                    const prevParagraph = currentParagraph.previousElementSibling
                    if (prevParagraph && prevParagraph.classList.contains('halo-paragraph')) {
                      const newRange = document.createRange()
                      const lastChild = prevParagraph.lastChild || prevParagraph
                      if (lastChild.nodeType === Node.TEXT_NODE) {
                        newRange.setStart(lastChild, lastChild.textContent.length)
                      } else {
                        newRange.setStart(prevParagraph, prevParagraph.childNodes.length)
                      }
                      newRange.collapse(true)

                      const selection = window.getSelection()
                      selection.removeAllRanges()
                      selection.addRange(newRange)
                    }

                    currentParagraph.remove()
                  }

                  event.preventDefault()
                  return
                }
              }

              // 尝试使用自定义删除处理
              const handled = me.handleDeleteInTopicContext(range, event.key)
              console.log('🔍 handleDeleteInTopicContext returned:', handled)
              if (handled) {
                console.log('🚫 Preventing default delete behavior')
                event.preventDefault()
                return
              }
              // 如果未处理，继续执行默认逻辑
              console.log('✅ Continuing with default delete logic')
            } else {
              console.log('🔍 wouldDeleteCauseNestedParagraph returned false')
            }
          }
          try {
            const selection = getSelection()
            if (
              me.selectDom &&
              me.moverClasses.includes(me.selectDom.className)
            ) {
              const range = document.createRange()
              range.selectNodeContents(me.selectDom.previousElementSibling)
              range.collapse(false)
              selection.removeAllRanges()
              selection.addRange(range)
              const removeDom = me.selectDom || event.target
              removeDom.remove()
              me.selectDom = null
              event.preventDefault()
            } else {
              if (selection.isCollapsed) {
                const node = selection.focusNode
                const offSet = selection.focusOffset
                const previousSibling =
                  node.previousElementSibling ||
                  node.parentNode.previousElementSibling ||
                  {}
                const isPre = me.moverClasses.includes(
                  previousSibling.className
                )
                if (isPre && offSet === 0) {
                  const curSelect =
                    previousSibling.querySelector('img') ||
                    previousSibling.querySelector('video')
                  if (curSelect) {
                    previousSibling.click()
                    previousSibling.focus()
                    event.preventDefault()
                  }
                }
              }
            }
          } catch (error) {
            console.log(error)
          }
        }

        // 处理cmd+left快捷键，当话题在行首时的特殊处理
        if ((event.metaKey || event.ctrlKey) && event.key === 'ArrowLeft') {
          console.log('🔍 [CMD+LEFT] Cmd+Left pressed')
          const selection = window.getSelection()
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            const container = range.startContainer

            // 找到当前段落
            let currentParagraph = container
            while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
              currentParagraph = currentParagraph.parentNode
            }
            while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
              currentParagraph = currentParagraph.parentNode
            }

            if (currentParagraph) {
              console.log('🔍 [CMD+LEFT] Current paragraph:', currentParagraph.outerHTML)
              console.log('🔍 [CMD+LEFT] Container:', container, 'Type:', container.nodeType)

              // 检查段落的第一个子元素是否是mdd-topic
              const firstChild = currentParagraph.firstChild
              console.log('🔍 [CMD+LEFT] First child:', firstChild, 'Tag:', firstChild?.tagName)

              if (firstChild && firstChild.tagName === 'MDD-TOPIC') {
                // 更精确的检查逻辑：检查光标是否在话题或话题后的内容上
                let shouldMoveToTopicStart = false
                const range = selection.getRangeAt(0)
                const offset = range.startOffset

                // 检查是否在话题元素内部
                if (container.nodeType === Node.TEXT_NODE && container.parentNode === firstChild) {
                  console.log('🔍 [CMD+LEFT] Case 1: In topic text node')
                  shouldMoveToTopicStart = true
                }
                // 检查是否在话题元素本身
                else if (container === firstChild) {
                  console.log('🔍 [CMD+LEFT] Case 2: On topic element')
                  shouldMoveToTopicStart = true
                }
                // 检查是否在话题后的空格上（文本节点）
                else if (container.nodeType === Node.TEXT_NODE && container.previousSibling === firstChild) {
                  console.log('🔍 [CMD+LEFT] Case 3: In text node after topic, offset:', offset)
                  shouldMoveToTopicStart = true
                }
                // 检查是否在话题后的span空格上
                else if (container.nodeType === Node.ELEMENT_NODE &&
                         container.tagName === 'SPAN' &&
                         container.previousSibling === firstChild) {
                  console.log('🔍 [CMD+LEFT] Case 4: In span after topic')
                  shouldMoveToTopicStart = true
                }
                // 检查是否在span空格内的文本节点
                else if (container.nodeType === Node.TEXT_NODE &&
                         container.parentNode &&
                         container.parentNode.tagName === 'SPAN' &&
                         container.parentNode.previousSibling === firstChild) {
                  console.log('🔍 [CMD+LEFT] Case 5: In text node inside span after topic')
                  shouldMoveToTopicStart = true
                }
                // 检查是否在段落中但在话题之后的任何位置
                else if (container === currentParagraph && offset > 0) {
                  // 检查offset位置是否在话题之后
                  const walker = document.createTreeWalker(
                    currentParagraph,
                    NodeFilter.SHOW_ALL,
                    null,
                    false
                  )
                  let nodeIndex = 0
                  let node
                  while (node = walker.nextNode()) {
                    if (node === firstChild) {
                      if (offset > nodeIndex) {
                        console.log('🔍 [CMD+LEFT] Case 6: In paragraph after topic')
                        shouldMoveToTopicStart = true
                      }
                      break
                    }
                    nodeIndex++
                  }
                }

                console.log('🔍 [CMD+LEFT] Should move to topic start:', shouldMoveToTopicStart)

                if (shouldMoveToTopicStart) {
                  // 阻止默认行为
                  event.preventDefault()

                  // 将光标设置到话题元素前面
                  const newRange = document.createRange()
                  newRange.setStartBefore(firstChild)
                  newRange.collapse(true)

                  selection.removeAllRanges()
                  selection.addRange(newRange)
                  console.log('🔍 [CMD+LEFT] Moved cursor to before topic')
                  return
                }
              }
            }
          }
        }

        // mark MDD-TOPIC 删除时需要整体删除，光标选中时禁止编辑、回车
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)
          const startContainer = range.startContainer
          let isInMddTopic = false
          let mddTopicElement = null

          if (
            startContainer.nodeType === Node.ELEMENT_NODE &&
            startContainer.tagName === 'MDD-TOPIC'
          ) {
            isInMddTopic = true
            mddTopicElement = startContainer
          } else if (startContainer.nodeType === Node.TEXT_NODE) {
            const parent = startContainer.parentNode
            if (parent && parent.tagName === 'MDD-TOPIC') {
              isInMddTopic = true
              mddTopicElement = parent
            }
          }

          if (isInMddTopic && mddTopicElement) {
            // 处理回车逻辑，禁止回车
            if (event.key === 'Enter') {
              event.preventDefault()
              return
            }

            // 处理删除逻辑（fn+delete在Mac上会触发Delete键事件）
            if (['Backspace', 'Delete'].includes(event.key)) {
              console.log('🔍 [TOPIC-DELETE] Deleting topic:', mddTopicElement.textContent)

              // 查找并删除相关的空格文本节点或span元素
              let spaceNode = null
              if (mddTopicElement.nextSibling) {
                const nextSibling = mddTopicElement.nextSibling
                console.log('🔍 [TOPIC-DELETE] Next sibling:', nextSibling, 'Type:', nextSibling.nodeType, 'Tag:', nextSibling.tagName)

                // 检查是否是文本节点空格
                if (nextSibling.nodeType === Node.TEXT_NODE && nextSibling.textContent === '\u00A0') {
                  spaceNode = nextSibling
                  console.log('🔍 [TOPIC-DELETE] Found text node space')
                }
              }

              // 设置光标位置
              const newRange = document.createRange()
              if (spaceNode) {
                // 如果有空格节点，将光标设置到空格节点后面
                newRange.setStartAfter(spaceNode)
              } else {
                // 否则设置到话题元素后面
                newRange.setStartAfter(mddTopicElement)
              }
              newRange.collapse(true)

              // 删除话题元素
              mddTopicElement.remove()

              // 删除相关的空格节点
              if (spaceNode) {
                spaceNode.remove()
              }

              // 设置新的光标位置
              selection.removeAllRanges()
              selection.addRange(newRange)

              // 更新编辑器状态
              me.updateData(true)

              event.preventDefault()
              return
            }

            // 处理禁止逻辑，禁止输入其他内容
            const isPrintableKey = event.key.length === 1
            if (isPrintableKey) {
              event.preventDefault()
              return
            }

            // 禁止粘贴等操作
            if (event.ctrlKey || event.metaKey) {
              if (event.key === 'v' || event.key === 'V') {
                event.preventDefault()
                return
              }
            }
          }
        }
      })
      me.editor.addEventListener('reAddListener', function (event) {
        me.setListener()
      })
      me.editor.addEventListener('willPaste', function (type, handler) {
        function validateDom() {
          const fragment = (type && type.fragment) || {}
          const textContent = fragment.textContent || ''
          let tmpNode = document.createElement('div')
          tmpNode.appendChild(fragment.cloneNode(true))
          const str = tmpNode.innerHTML
          // const text = tmpNode.innerText || ""
          // 含图片、视频、多段文字 禁止原始格式粘贴 || textContent.length > 200
          // const isTextNode = fragment.childNodes.length === 1 && fragment.childNodes[0].nodeName === '#text'
          let isText = false
          const notDefault =
            str.indexOf('<img') > -1 || str.indexOf('<video') > -1
          if (!notDefault) {
            isText= true
            // const frag = document.createDocumentFragment()
            // const div = document.createElement('p')
            // div.innerText = textContent
            // div.className = 'halo-paragraph'
            // frag.appendChild(div)
            // type.fragment = frag
          }
          type.defaultPrevented = notDefault
          tmpNode = null
          return isText
        }
        const isText = validateDom()
        if(isText){
          type.defaultPrevented = true
          // windows上企业微信文本无法复制，mac上可以
          if(type.fragment.children?.length === 0) {
            const frag = document.createDocumentFragment()
            const div = document.createElement('p')
            div.innerText = type.fragment.textContent
            div.className = 'halo-paragraph'
            frag.appendChild(div)
            type.fragment = frag
            type.defaultPrevented = false

            return
          }
          me.setStickerTextContent(type.fragment.children)
          return
        }

        // 处理粘贴内容中的mdd-topic元素
        if (type.fragment) {
          const mddTopics = type.fragment.querySelectorAll('mdd-topic')
          mddTopics.forEach(topic => topic.setAttribute('contenteditable', 'false'))
        }
        if (type.defaultPrevented) {
          me.getSticker(type, handler)
        }
      })
      // event end

      window.Squire.prototype.makeParagraph = function (
        content,
        position,
        textAlign
      ) {
        console.log('🔍 [ENTER-FIX] makeParagraph called with content:', content)

        const className = textAlign
          ? `halo-paragraph align-${textAlign}`
          : 'halo-paragraph'
        let p = this.createElement('p', {
          class: className,
        })
        if (textAlign) {
          p.style['text-align'] = textAlign
        }
        if (position) {
          p = this.createElement('DIV', {
            class: 'halo-paragraph',
            contenteditable: 'true',
            'data-position': position,
          })
        }

        // 确保空段落包含<br>标签
        if (!content || content.trim() === '') {
          p.innerHTML = '<br>'
          console.log('🔍 [ENTER-FIX] makeParagraph: Created empty paragraph with <br>')
        } else {
          p.innerHTML = content
          console.log('🔍 [ENTER-FIX] makeParagraph: Created paragraph with content:', content)
        }

        this.insertElement(p)
      }

      // 覆盖Squire的默认回车键处理
      const originalEnterHandler = window.Squire.prototype._keyHandlers?.enter
      if (originalEnterHandler) {
        window.Squire.prototype._keyHandlers.enter = function(self, event, range) {
          console.log('🔍 [ENTER-FIX] Squire enter handler triggered')

          // 检查是否需要自定义处理
          const startContainer = range.startContainer
          console.log('🔍 [ENTER-FIX] Enter startContainer:', startContainer)

          // 检查是否在mdd-topic内部或包含mdd-topic的段落中
          let isInMddTopic = false
          let currentParagraph = startContainer

          // 向上查找mdd-topic
          let node = startContainer
          while (node && node !== self._root) {
            if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'MDD-TOPIC') {
              isInMddTopic = true
              break
            }
            node = node.parentNode
          }

          // 向上查找当前段落
          while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
            currentParagraph = currentParagraph.parentNode
          }
          while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
            currentParagraph = currentParagraph.parentNode
          }

          console.log('🔍 [ENTER-FIX] Current paragraph:', currentParagraph?.outerHTML)
          console.log('🔍 [ENTER-FIX] Is in topic:', isInMddTopic)
          console.log('🔍 [ENTER-FIX] Paragraph has topic:', currentParagraph && currentParagraph.querySelector('mdd-topic'))

          // 如果在mdd-topic内部或段落包含mdd-topic，让Vue组件处理
          if (isInMddTopic || (currentParagraph && currentParagraph.querySelector('mdd-topic'))) {
            console.log('🔍 [ENTER-FIX] Letting Vue component handle enter')
            // 不阻止事件，让Vue组件的keydown监听器处理
            return
          }

          console.log('🔍 [ENTER-FIX] Using original Squire enter handler')
          // 调用原始处理逻辑，但确保创建的段落包含<br>
          const result = originalEnterHandler.call(this, self, event, range)

          // 检查是否创建了新段落，如果是空的则添加<br>
          setTimeout(() => {
            const selection = self.getSelection()
            if (selection.rangeCount > 0) {
              const newRange = selection.getRangeAt(0)
              let newParagraph = newRange.startContainer

              // 向上查找段落
              while (newParagraph && newParagraph.nodeType !== Node.ELEMENT_NODE) {
                newParagraph = newParagraph.parentNode
              }
              while (newParagraph && !newParagraph.classList?.contains('halo-paragraph')) {
                newParagraph = newParagraph.parentNode
              }

              if (newParagraph && newParagraph.innerHTML.trim() === '') {
                newParagraph.innerHTML = '<br>'
                console.log('🔍 [ENTER-FIX] Added <br> to empty paragraph after Squire enter')
              }
            }
          }, 0)

          return result
        }
      }

      // 覆盖Squire的默认删除键处理
      console.log('🔍 Checking Squire _keyHandlers:', window.Squire.prototype._keyHandlers)

      const originalBackspaceHandler = window.Squire.prototype._keyHandlers?.backspace
      const originalDeleteHandler = window.Squire.prototype._keyHandlers?.delete

      console.log('🔍 Original backspace handler:', originalBackspaceHandler)
      console.log('🔍 Original delete handler:', originalDeleteHandler)

      if (originalBackspaceHandler) {
        window.Squire.prototype._keyHandlers.backspace = function(self, event, range) {
          console.log('🔍 Squire backspace handler triggered')

          // 检查是否在空段落中
          const startContainer = range.startContainer
          let currentParagraph = startContainer

          // 向上查找当前段落
          while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
            currentParagraph = currentParagraph.parentNode
          }
          while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
            currentParagraph = currentParagraph.parentNode
          }

          if (currentParagraph) {
            const paragraphContent = currentParagraph.innerHTML.trim()
            console.log('🔍 Squire backspace - paragraph content:', paragraphContent)

            // 如果是空段落，阻止Squire的默认处理
            if (paragraphContent === '<br>' || paragraphContent === '') {
              console.log('🚫 Blocking Squire backspace for empty paragraph')
              // 直接删除空段落
              if (currentParagraph.parentNode && currentParagraph.parentNode.children.length > 1) {
                currentParagraph.remove()
              }
              return // 阻止默认处理
            }
          }

          console.log('✅ Using original Squire backspace handler')
          // 否则使用原始处理逻辑
          return originalBackspaceHandler.call(this, self, event, range)
        }
      }

      if (originalDeleteHandler) {
        window.Squire.prototype._keyHandlers.delete = function(self, event, range) {
          console.log('🔍 Squire delete handler triggered')

          // 检查是否在空段落中
          const startContainer = range.startContainer
          let currentParagraph = startContainer

          // 向上查找当前段落
          while (currentParagraph && currentParagraph.nodeType !== Node.ELEMENT_NODE) {
            currentParagraph = currentParagraph.parentNode
          }
          while (currentParagraph && !currentParagraph.classList?.contains('halo-paragraph')) {
            currentParagraph = currentParagraph.parentNode
          }

          if (currentParagraph) {
            const paragraphContent = currentParagraph.innerHTML.trim()
            console.log('🔍 Squire delete - paragraph content:', paragraphContent)

            // 如果是空段落，阻止Squire的默认处理
            if (paragraphContent === '<br>' || paragraphContent === '') {
              console.log('🚫 Blocking Squire delete for empty paragraph')
              // 直接删除空段落
              if (currentParagraph.parentNode && currentParagraph.parentNode.children.length > 1) {
                currentParagraph.remove()
              }
              return // 阻止默认处理
            }
          }

          console.log('✅ Using original Squire delete handler')
          // 否则使用原始处理逻辑
          return originalDeleteHandler.call(this, self, event, range)
        }
      }

      // fn+delete在Mac上实际上会触发Delete键事件，不需要单独的键处理器
      // 在keydown事件中已经通过检查event.key === 'Delete'来处理

      // 覆盖Squire的createDefaultBlock方法，确保空段落包含<br>
      const originalCreateDefaultBlock = window.Squire.prototype.createDefaultBlock
      if (originalCreateDefaultBlock) {
        window.Squire.prototype.createDefaultBlock = function(children) {
          console.log('🔍 Squire createDefaultBlock called with children:', children)
          const block = originalCreateDefaultBlock.call(this, children)

          console.log('🔍 Created block:', block)
          console.log('🔍 Block classList:', block?.classList)
          console.log('🔍 Block innerHTML:', block?.innerHTML)

          // 如果创建的是空段落，确保包含<br>
          if (block && block.classList && block.classList.contains('halo-paragraph')) {
            const content = block.innerHTML.trim()
            console.log('🔍 Block content check:', content)
            if (!content || content === '') {
              block.innerHTML = '<br>'
              console.log('🔍 createDefaultBlock: Added <br> to empty paragraph')
            } else {
              console.log('🔍 createDefaultBlock: Block already has content')
            }
          } else {
            console.log('🔍 createDefaultBlock: Block is not halo-paragraph or is null')
          }

          return block
        }
      }

      // 覆盖Squire的splitBlock方法，确保分割后的段落包含<br>
      const originalSplitBlock = window.Squire.prototype.splitBlock
      if (originalSplitBlock) {
        window.Squire.prototype.splitBlock = function(root, startNode, startOffset, endNode, endOffset) {
          console.log('🔍 Squire splitBlock called')
          const result = originalSplitBlock.call(this, root, startNode, startOffset, endNode, endOffset)

          // 检查分割后的段落，确保空段落包含<br>
          if (result && result.nodeType === Node.ELEMENT_NODE) {
            const paragraphs = result.querySelectorAll('.halo-paragraph')
            paragraphs.forEach(p => {
              const content = p.innerHTML.trim()
              if (!content || content === '') {
                p.innerHTML = '<br>'
                console.log('🔍 splitBlock: Added <br> to empty paragraph')
              }
            })
          }

          return result
        }
      }

      window.Squire.prototype.makeHeader = function (
        content,
        config = { makeHeader: {} }
      ) {
        console.log(content)
        if (content) {
          const h2 = this.createElement('h2', {
            class: 'halo-paragraph-title',
          })
          h2.innerHTML = content
          this.insertElement(h2)

          return
        }
        let container
        this.modifyBlocks(function (frag) {
          var output = this._doc.createDocumentFragment()
          var block = frag
          let dom
          const nodeName = block.firstChild && block.firstChild.nodeName
          if (
            (nodeName === 'P' && config.makeHeader.type === 0) ||
            (nodeName === 'H2' && config.makeHeader.type === 1)
          ) {
            dom = nodeName
          } else {
            dom = nodeName === 'H2' ? 'P' : 'H2'
          }
          const cla = dom === 'H2' ? 'halo-paragraph-title' : 'halo-paragraph'
          // eslint-disable-next-line no-cond-assign
          while ((block = window.Squire.getNextBlock(block))) {
            container = this.createElement(dom, { class: cla }, [
              window.Squire.empty(block),
            ])
            // debugger
            output.appendChild(
              // 段落才能添加标题
              Array.from(block.classList).indexOf('halo-img-content') > -1
                ? block
                : container
            )
          }

          return output
        })
        const selection = window.getSelection()
        const range = document.createRange()
        console.log(container)
        range.setStart(container, 1)
        range.collapse(true)
        selection.removeAllRanges()
        selection.addRange(range)
      }
      window.Squire.prototype.insertImages = function (src, data = {}, range) {
        // 1、不处理已上传失败情况了  2、每次粘贴或缓存获取，重新计算待上传图片
        var isHaloImage = me.isHaloImage(src)
        var scrollTop = document.documentElement.scrollTop
        const isSeameless = data.seamlessFlag === '1'
        // 撤回记录滚动位置，未处理
        var loading = this.createElement('div', {
          class: 'img-loading',
          contenteditable: 'false',
        })
        loading.innerHTML = `
          <img class="img-loading-icon" src="/img/upload-image-loading.png"/>
          <span class="img-loading-tip">上传中...</span>
        `
        var fail = this.createElement('div', {
          class: 'img-fail hide',
          contenteditable: 'false',
        })
        fail.innerHTML = '上传失败<br>请下载图片至本地后重新上传'
        var again = this.createElement('button', {
          class: 'img-again hide',
          contenteditable: 'false',
        })
        again.innerHTML = '重新上传'

        // var onerror = 'onerror=null;src="/static/img/web_img_zhanwei.png"';
        const isGif = src && src.indexOf('.gif') > -1
        if (isGif) {
          src = src.replace('.gif', '.gif!nowater')
        }
        var img = this.createElement('img', {
          id: isHaloImage ? '' : src,
          src: src,
          class: isHaloImage
            ? 'halo-picture-area'
            : 'halo-picture-area no-upload',
          data: JSON.stringify(data),
          style: isSeameless ? 'margin: 0' : 'margin-top: 0',
          'data-content': data.content || '',
          contenteditable: 'false',
          draggable: 'false',
          tabindex: '-1',
        })
        const btns = me.insertImgOperateBtns(this, data)
        var childList = []
        childList.push(img)
        childList.push(...btns)
        !isHaloImage && childList.push(loading)
        !isHaloImage && childList.push(fail)
        !isHaloImage && childList.push(again)
        var p = this.createElement(
          'div',
          {
            class: 'halo-img-content',
            contenteditable: 'false',
            draggable: 'true',
            tabindex: '-1',
          },
          [...childList]
        )
        me.setListener(p)
        this.insertElement(p, range)
        if (isSeameless) {
          //移除插入图片默认增加的p标签
          me.removeDefault(p, 'nextElementSibling')
          me.removeDefault(p, 'previousElementSibling')
        } else {
          //移除插入图片上方的空p标签
          const pre = p.previousElementSibling
          const isBlock = pre?.classList.contains('halo-paragraph') || false
          if (isBlock && (!pre.textContent || pre.innerText === '\n')) {
            pre.remove()
          }
        }
        setTimeout(() => {
          document.documentElement.scrollTop = scrollTop
        }, 50)
      }

      window.Squire.prototype.insertVideoProgress = function () {
        const div = this.createElement('DIV', {
          class: 'progress-wrap',
        })
        div.innerHTML = `
          <div class="video-progress">
            <div class="label">上传中...</div>
            <div class="box">
              <div class="inner" style="width:20%"></div>
            </div>
          </div>
        `
        this.insertElement(div)
      }
      window.Squire.prototype.insertArticle = function (dom) {
        this.insertElement(dom)
      }
      window.Squire.prototype.insertRiding = function (dom) {
        this.insertElement(dom)
      }
      window.Squire.prototype.insertVideo = function (src, data = {}) {
        var scrollTop = document.documentElement.scrollTop
        var video = this.createElement('VIDEO', {
          src: src,
          class: 'halo-video-area',
          controls: 'controls',
          data: JSON.stringify(data),
          'data-content': data.content || '',
          poster: data.img || '',
          // 'contenteditable': 'true',
        })
        var delBtn = me.genIconDom({
          class: 'pointer video-delete icon',
          contenteditable: 'false',
          onClick(e) {
            me.removeParentByClass(e.target, 'halo-video-content')
            e.stopPropagation()
          },
        })
        console.log(delBtn)
        // delBtn.onclick = function(e) {
        //   me.removeParentByClass(e.target, 'halo-video-content')
        //   e.stopPropagation()
        // }
        const posterBtn = me.insertVideoBtns(this, video)
        var p = this.createElement(
          'DIV',
          {
            class: 'halo-video-content',
            contenteditable: 'false',
            'data-video': JSON.stringify(data),
            draggable: 'true',
            tabindex: '-1',
          },
          [video, delBtn, posterBtn]
        )
        me.setListener(p)
        this.insertElement(p)
        setTimeout(() => {
          document.documentElement.scrollTop = scrollTop
        }, 100)
      }
      window.Squire.prototype.insertLink = function (link, text) {
        var scrollTop = document.documentElement.scrollTop
        var img = `<img class="link-img" src='/static/img/<EMAIL>'></img>`
        var a = `<a class="halo-link-mes"  data-url="${link}" target="_blank">${text}</a>`
        const del = me.generateDelLinkIcon()
        var p = this.createElement('DIV', {
          class: 'halo-link',
          contenteditable: 'false',
        })
        p.innerHTML = img + a
        p.appendChild(del)
        this.insertElement(p)
        setTimeout(() => {
          document.documentElement.scrollTop = scrollTop
        }, 50)
      }
      me.sticky()
    },

    setDragMove(p) {
      const className = p.getAttribute('class')
      const me = this
      p.ondragstart = function (e) {
        if (!(e.target.className.indexOf(className) < 0)) {
          e.dataTransfer.setData('text/html', e.target)
          e.dataTransfer.effectAllowed = 'move'
          e.dataTransfer.dropEffect = 'move'
          me.targetMove = e.target
        }
      }
    },

    // 撤销重做 会把 所有的监听全都移除，所以需要重新绑定
    setListener(dom) {
      const me = this
      const doms = dom ? [dom] : Array.from(this.editorDom.children)
      doms.forEach((p) => {
        if (me.moverClasses.includes(p.className)) {
          me.setDragMove(p)
          const area = p.firstChild
          p.onclick = function (e) {
            if (
              area.nodeName === 'VIDEO' &&
              me.selectDom?.className === 'halo-video-content'
            ) {
              me.setToast('不支持播放')
            }
            if (e.target.className.includes('desc-input')) {
              return
            }
            me.selectDom = p
            const selected = document.querySelector('.halo-select')
            selected && selected.classList.remove('halo-select')
            area.classList.add('halo-select')
            p.focus()
            // me.setToast('不支持播放');
          }
          p.onblur = function (e) {
            area.classList.remove('halo-select')
            me.selectDom = null
          }
          const deleteVideo = p.querySelector('.video-delete')
          const deleteImg = p.querySelector('.img-delete')
          if (deleteImg) {
            deleteImg.onclick = function (e) {
              if (me.disabled) return
              me.removeSeamlessLaster(e)
              p.remove()
              me.updateData(true)
            }
          }
          if (deleteVideo) {
            deleteVideo.onclick = function () {
              p.remove()
            }
          }
        }
      })
    },

    removeDefault(next, key) {
      const me = this
      const cur = next[key]
      if (!cur || cur.innerText !== '\n') return
      //全部移除p标签会使rang的值变化导致替换图片时位置不对，所以保留图片前后各一个隐藏的p标签，用来确定位置
      const blockDefault = cur.classList.contains('halo-paragraph') || false
      const isLasterBlock = next.classList.contains('halo-paragraph') || false
      if (blockDefault && isLasterBlock) {
        cur.remove()
        me.removeDefault(cur, key)
      } else {
        if (blockDefault) {
          cur.style.display = 'none'
          me.removeDefault(cur, key)
        }
      }
    },
    generateDelLinkIcon() {
      const me = this
      const del = this.editor.createElement('img', {
        class: 'img-delete',
        contenteditable: 'false',
        src: car_ciose,
      })
      del.onclick = function (e) {
        const parent = me.findParentByClass(e.target, 'halo-link')
        parent.remove()
      }
      return del
    },
    // 转换数据，返回给业务
    getEditorData() {
      const me = this
      const children = Array.from(me.editorDom.children)
      let isNotParagraph = true
      let count = 0
      let imgCount = 0
      let title = 0
      let videoIds = []
      let hasFailUploadImg = false
      let cardEassyFlagStatus = false
      this.hasArticleCard = false
      children.map(function (value, index) {
        if (value.tagName === 'H2' && value.innerText.length) {
          // 段落标题
          isNotParagraph = false
          title += value.innerText.length
        } else if (value.className.includes('article-wrap')) {
          const article = JSON.parse(value.dataset.article)
          cardEassyFlagStatus = !!article.cardEassyFlag
          me.hasArticleCard = true
        } else if (
          value.className.includes('halo-paragraph') ||
          value.tagName === 'OL' ||
          value.tagName === 'UL'
        ) {
          // 文字
          const text = value.innerText && value.innerText.trim()
          if (text.length < 1) {
            return
          }
          count += text.replace(/\n/g, '').length
          isNotParagraph = false
        } else if (value.className === 'halo-img-content') {
          // 图文
          const img = value.querySelector('.halo-picture-area')
          isNotParagraph = false
          if (!me.isHaloImage(img.src)) {
            hasFailUploadImg = true
            return
          }
          imgCount += 1
        } else if (value.className === 'halo-video-content') {
          // 视频
          const id = JSON.parse(value.dataset.video).id
          videoIds.push(id)
          isNotParagraph = false
        } else if (value.className === 'halo-link') {
          // 外链
          isNotParagraph = false
        } else {
          // 默认文字
          if (value.innerText.length < 1) {
            return
          }
          isNotParagraph = false
        }
      })
      return {
        isNotParagraph,
        hasFailUploadImg,
        contentLen: count,
        imgCount: imgCount,
        titleCount: title,
        cardEassyFlagStatus,
        videoIds,
      }
    },

    // 是否是摩托范的图片
    isHaloImage(url = '') {
      return (
        url.indexOf('jddmoto') > -1 ||
        url.indexOf('58moto') > -1 ||
        url.indexOf('emotofine') > -1
      )
    },
    // 获取待上传图片列表
    updateUploads() {
      const me = this
      setTimeout(() => {
        const uploads = document.querySelectorAll('.no-upload') || []
        me.uploadStore = {}
        me.currentIndex = 0

        Array.from(uploads).map((_, index) => {
          const id = _.getAttribute('id')
          const key = index + '|' + id
          me.uploadStore[key] = _ // 1 no-upload 2 upload-fail
        })
        me.uploadImageHandler()
      }, 500)
    },
    uploadImageHandler(key = '') {
      const me = this
      const list = Object.keys(me.uploadStore) // 待上传图片集合

      if (!list.length || me.currentIndex > list.length) return

      const one = 1
      const two = 2
      const three = 3
      const moreImage = list.length > three

      // 多张图片，每三张并发上传
      if (me.currentIndex) {
        // 1、非首次上传
        if (moreImage) {
          // 接力第一、二、三线程池
          me.uploadImage(key).then((_) =>
            me.uploadImageHandler(list[me.currentIndex++])
          )
        }
      } else {
        // 2、首次上传
        // 第一个线程池
        me.uploadImage(list[0]).then((_) =>
          me.uploadImageHandler(list[me.currentIndex++])
        )
        // 第二个线程池
        list.length > one &&
          me
            .uploadImage(list[one])
            .then((_) => me.uploadImageHandler(list[me.currentIndex++]))
        // 第三个线程池
        list.length > two &&
          me
            .uploadImage(list[two])
            .then(
              (_) => moreImage && me.uploadImageHandler(list[me.currentIndex++])
            )
        me.currentIndex = three // 更新上传位置
      }
    },
    uploadImage(key = '') {
      const me = this
      const itemId = me.guid || ''
      const currentImg = me.uploadStore[key]
      const imageUrl = key.split('|')[1] || ''

      if (!currentImg) {
        console.log('图片不存在', key)
        return Promise.reject('图片不存在')
      }

      const parent = currentImg.parentNode || ''
      const uploadFailHandler = (currentImg) => {
        currentImg.className = 'halo-picture-area upload-fail'

        const imgLoading = parent.querySelector('.img-loading')
        imgLoading && (imgLoading.className = 'img-loading hide')
        const imgReplace = parent.querySelector('.img-replace')
        const input = parent.querySelector('.desc-input-wrap')
        imgReplace && parent.removeChild(imgReplace)
        input && parent.removeChild(input)
        const imgFail = parent.querySelector('.img-fail')
        imgFail && (imgFail.className = 'img-fail')
        const imgAgain = parent.querySelector('.img-again')
        if (imgAgain) {
          imgAgain.className = 'img-again'
          imgAgain.onclick = function (e) {
            me.currentImg = currentImg
            const upload = document.querySelector('.upload-again-input')
            upload && upload.click()
          }
        }
      }
      return this.uploadImageByOther({
        itemId,
        imageUrl,
        businessId: 2,
      })
        .then((_) => {
          // https://mp.weixin.qq.com/s?__biz=MjM5NzY4MzQyMQ%3D%3D&chksm=bed7fa9e89a07388f045b67d3749aa94e9df2dae8ebdae57efa5956bfa4b9b7554538beec7a6&idx=1&mid=2650084140&scene=0&sn=0fbf85ba93be4e226304be2df10b3ea6#rd
          // && imageUrl !== 'https://mmbiz.qpic.cn/mmbiz_gif/lxslyr6ibEMsfJRVAPrYkTX5ibNCWvZlfbXgH2NRR4esGTdQGzvibVbnkqAHibSviaC6nIEAibm9Ke9Ipa6tnAu8xc8w/640?wx_fmt=gif'
          if (_.data.code === 0 && _.data.data) {
            const img = _.data.data.thirdPartyImageUrl
            me.updateImage(img, currentImg)
          } else {
            parent && uploadFailHandler(currentImg)
          }
          if (
            Object.keys(me.uploadStore).length ===
            Number(key.split('|')[0] || '') + 1
          ) {
            // 上传为最后一张时，更新数据
            setTimeout(() => {
              me.updateData(true)
            }, 100)
          }
        })
        .catch((e) => {
          console.log(e)
          parent && uploadFailHandler(currentImg)
        })
    },
    // 更新上传的图片
    updateImage(img, currentImg = {}) {
      const data = JSON.parse(currentImg.getAttribute('data') || '{}')
      data.img = img

      currentImg.setAttribute('src', img)
      currentImg.setAttribute('data', JSON.stringify(data))
      currentImg.className = 'halo-picture-area'

      const parent = currentImg.parentNode || ''
      // debugger
      const loading = parent.querySelector('.img-loading')
      const imgFail = parent.querySelector('.img-fail')
      const imgAgain = parent.querySelector('.img-again')
      loading && parent.removeChild(loading)
      imgFail && parent.removeChild(imgFail)
      imgAgain && parent.removeChild(imgAgain)
      const desc = parent.querySelector('.desc-input-wrap')
      if (!desc) {
        const arr = this.insertImgOperateBtns(this.editor, {
          seamlessFlag: data.seamlessFlag || '0',
        })
        parent.appendChild(arr[0])
        parent.appendChild(arr[2])
      }
      // parent && (parent.innerHTML = currentImg.outerHTML)
    },
    // 获取ctrl + v 后内容
    getSticker(type, handler) {
      this.setSticker(type.fragment.children)
    },
    setStickerTextContent(data){
      const me = this
      data = Array.from(data)
      // debugger
      data.map(function (value, index) {
        // 其他标签时，需要针对嵌套标签做截取
        const htmlArr = (value.innerHTML || value.outerHTML) // 剪切图片用到 value.outerHTML
        
        .replace(/<p[^>]*>(.*?)<\/p>/gi, '(delimiter)$1(delimiter)')
      // 删除除 p 外的所有其他标签（保留文本）
      .replace(/<(?!\/?p\b)[^>]+>/gi, '')
      // 删除残留的 p 标签（如 </p>）
      .replace(/<\/?p[^>]*>/gi, '')
      // 原有逻辑：删除所有标签并拆分
      .replace(/<\/?.+?>/g, '')
      .split('(delimiter)');

        htmlArr.map(function (nValue) {
          if (!nValue || nValue === '&nbsp;') {
            return
          }
          me.editor.makeParagraph(nValue)

        })
      })

      this.updateData(true)
    },
    // 粘贴
    setSticker(data) {
      const me = this
      data = Array.from(data)
      // debugger
      data.map(function (value, index) {
        // 其他标签时，需要针对嵌套标签做截取
        const htmlArr = (value.innerHTML || value.outerHTML) // 剪切图片用到 value.outerHTML
          .replace(
            /<img(.*?)src="(.*?)"(.*?)>/g,
            '(delimiter)[img src=$2](delimiter)'
          )
          .replace(
            /<img(.*?)src='(.*?)'(.*?)>/g,
            '(delimiter)[img src=$2](delimiter)'
          )
          .replace(
            /<a href="(.*?)"(.*?)>(.*?)<\/a>/g,
            '(delimiter)$3(delimiter)'
          )
          .replace(/<p (.*?)>(.*?)<\/p>/g, '(delimiter)$2(delimiter)')
          .replace(/<\/?.+?>/g, '') // 截取出img、link 和文字部分，使用(delimiter) 切割
          .split('(delimiter)') // 拆分成数组，完成替换

        htmlArr.map(function (nValue) {
          if (!nValue || nValue === '&nbsp;') {
            return
          }
          if (nValue.indexOf('[img') > -1) {
            // 图片
            const src = nValue
              .replace(/\[img src=(.*?)\]/g, '$1')
              .replace(/amp;/g, '')
            me.editor.insertImages(src, {
              src,
              img: src,
              selected: false,
              type: '2',
            })
          } else {
            // 文字
            me.editor.makeParagraph(nValue)
          }
        })
      })

      me.updateUploads()
      this.updateData(true)
    },
    mouseLeave() {
      const me = this
      me.updateData()
    },
    // 监测 添加、撤回、恢复、删除
    setBack(e, type) {
      const me = this
      me.refreshImg()
      me.updateData(true)
    },
    refreshImg() {
      Array.from(document.querySelectorAll('.halo-img-content')).map(
        (_ = {}) => {
          var target = _.querySelector('.halo-picture-area')
          var tag = null
          if (target?.classList?.contains('upload-fail')) {
            tag = _.querySelector('.img-again')
          } else {
            tag = _.querySelector('.desc-input')
          }
          if (!target || !tag) {
            if (_.parentNode) {
              _.parentNode.removeChild(_)
            }
          }
          //
        }
      )

      Array.from(document.querySelectorAll('.article-wrap')).map((_ = {}) => {
        var target = _.querySelector('.icon')
        if (!target) {
          if (_.parentNode) {
            _.parentNode.removeChild(_)
          }
        }
        //
      })
    },
    // 设置scoll
    sticky() {
      const me = this
      me.$nextTick(function () {
        const sticky = document.querySelector('.tool-box')
        if (!sticky) {
          return
        }
        const origOffsetY = sticky.offsetTop
        function onScroll(e) {
          if (location.href.indexOf('/detail') === -1) {
            document.removeEventListener('scroll', onScroll)
            return
          }
          window.scrollY >= origOffsetY + 160
            ? sticky.classList.add('fixed-top')
            : sticky.classList.remove('fixed-top')
        }
        document.removeEventListener('scroll', onScroll)
        document.addEventListener('scroll', onScroll)
      })
    },
    // ==========toolbox==========
    // 撤回、恢复、段落标题
    setContent(e, obj) {
      const me = this
      const id = e.target.id || e.target.dataset.editorId
      const value = ''
      if (id && me.editor && me.editor[id]) {
        me.editor[id](value, obj)
        me.updateData(true)
      }
    },
    canSetAlign() {
      const editor = this.editor
      if (editor) {
        const selection = editor.getSelection()
        const container = selection.startContainer
        if (
          container.parentNode &&
          container.parentNode.classList.contains('halo-paragraph-title')
        ) {
          this.setAlignFlag = false
          return
        }
        if (
          container &&
          container.classList &&
          container.classList.contains('halo-paragraph-title')
        ) {
          this.setAlignFlag = false
          return
        }
        this.setAlignFlag = true
      }
    },
    setAlign(e, align) {
      const id = e.target.dataset.editorId
      if (id && this.editor && this.editor[id]) {
        this.editor[id](align)
        this.updateData(true)
      }
    },
    setColor(val) {
      this.editor['setTextColour'](val)
      this.updateData(true)
    },
    setFontSize(e, size) {
      this.editor['setFontSize'](size)
      this.fontInfo = this.editor.getFontInfo()
      this.updateData(true)
    },
    // 增加link
    addLink(type) {
      const me = this
      me.linkTitleName = type === 'link' ? '添加链接' : '添加导入链接地址'
      me.linkPlaceholder =
        type === 'link' ? '请输入链接地址' : '仅支持输入微信链接'
      me.viewStatus = true
      me.linkStatus = true
    },
    validUrl(url) {
      const objExp =
        /^(((ht|f)tp(s?)):\/\/)?(www.|[a-zA-Z].)[a-zA-Z0-9-.]+.(com|edu|gov|mil|net|org|biz|info|name|museum|us|ca|uk|cn|im)(:[0-9]+)*(\/($|[a-zA-Z0-9.,;?'&%$#=~_-]+))*$/
      return objExp.test(url)
    },
    // 确认增加link
    confirmLink() {
      const me = this
      if (!me.linkContent) {
        return ElMessage.error('请输入链接地址！')
      }
      if (
        me.linkTitleName === '添加导入链接地址' ||
        this.visibleCollectArticle
      ) {
        this.visibleCollectArticle = false
        return me.importLinkData() // 一键导入功能单独处理
      }
      if (!this.validUrl(me.linkContent)) {
        return me.setToast('URL无效')
      }
      if (!me.isHaloImage(me.linkContent)) {
        return me.setToast('请输入摩托范内部网址')
      }
      me.editor['insertLink'](me.linkContent)
      me.linkContent = ''
      me.closeDialog()
    },
    // 增加link导入数据
    importLinkData() {
      const me = this
      if (me.loading) {
        return ElMessage('正在获取信息，请稍后...')
      }
      me.loading = true
      const params = {
        action: me.isOss ? '30008OSS' : 30008,
        url: me.linkContent,
      }
      if (!me.isOss) {
        params.autherid = me.user.uid
      }
      this.importEssay(params)
        .then((response) => {
          console.log(response)
          if (response.status === 200 && response.data) {
            const data = JSON.parse(response.data || '{}')
            data.data.content = data.data.content
              .replace(/<a href="(.*?)"(.*?)>/g, '')
              .replace(/<\/a>/g, '')
              .replace(/<iframe([^<>]*)>([^<>]*)<\/iframe>/g, '')
              .replace(/<video([^<>]*)>([^<>]*)<\/video>/g, '')
            // 运营需求，删除a标签（不删除内容），删除video、iframe 标签
            const importContent = document.createElement('div')
            importContent.innerHTML = data.data.content
            me.setSticker(importContent.children) // 重新走粘贴方法
            me.closeDialog()
            me.linkContent = ''
            return
          } else {
            me.setToast('导入失败，请重试')
          }
        })
        .catch((err) => {
          console.log(err.message)
          me.setToast('导入异常，请重试')
        })
        .finally((_) => {
          me.loading = false
        })
    },
    // 打开 图片、视频、一键导入 弹框
    openDialog(name) {
      const me = this
      me.viewName = name === 'img' ? '添加图片' : '添加视频'
      me.viewButton = name === 'img' ? '选择图片' : '选择视频'
      me.viewTip =
        name === 'img'
          ? ''
          : '为了获得更高的推荐和点击量，建议上传720p(1280*720)，大小不超过500MB(视频上传需要时间，请耐心等待)'
      me.viewStatus = true
    },
    // 关闭弹框
    closeDialog() {
      this.viewStatus = false
      this.linkStatus = false
      this.linkContent = ''
    },
    // ==========toolbox end==========
    // 弹框提示
    setToast(content) {
      ElMessage.error(content)
    },
    setMessageBoxNoCancel(content) {
      ElMessageBox.confirm(content, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {})
        .catch(() => {})
    },

    // 话题弹框相关方法
    showTopicPopover(type, position, searchKeyword = '', triggerInfo = null) {
      // 检查必要的依赖
      if (!this.request || !this.request.getTopic) {
        console.warn('话题功能需要提供 request.getTopic 方法')
        return
      }

      // 保存当前的selection和range信息
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        this.topicPopover.originalRange = selection.getRangeAt(0).cloneRange()
        this.topicPopover.originalSelection = selection
      }

      // 保存触发信息
      if (triggerInfo) {
        this.topicPopover.triggerInfo = triggerInfo
      }

      this.topicPopover.visible = true
      this.topicPopover.type = type
      this.topicPopover.position = position
      this.topicPopover.searchKeyword = searchKeyword

      if (type === 'hot') {
        this.topicPopover.activeTab = 'hot'
        this.loadHotTopics()
        this.loadRecentTopics()
      } else if (type === 'search') {
        this.searchTopics(searchKeyword)
      }
    },

    hideTopicPopover() {
      this.topicPopover.visible = false
      this.topicPopover.hotTopics = []
      this.topicPopover.searchTopics = []
      this.topicPopover.page = 1
      this.topicPopover.searchPage = 1
      this.topicPopover.hasMore = true
      this.topicPopover.searchHasMore = true
      // 清理保存的信息
      this.topicPopover.originalRange = null
      this.topicPopover.originalSelection = null
      this.topicPopover.triggerInfo = {
        paragraph: null,
        hashIndex: -1,
        cursorPosition: 0
      }
    },

    switchTopicTab(tab) {
      this.topicPopover.activeTab = tab
    },

    async loadHotTopics() {
      if (this.topicPopover.loading || !this.topicPopover.hasMore) return

      this.topicPopover.loading = true
      try {
        const response = await this.request.getTopic({
          action: '201023',
          page: this.topicPopover.page,
          limit: 50,
          hoopId: 0,
          type: 0,
          orderBy: 'view'
        })

        if (response && response.data && response.data.code === 0) {
          const topics = response.data.data || []
          if (topics.length === 0) {
            this.topicPopover.hasMore = false
          } else {
            // 过滤重复的话题
            const existingIds = new Set(this.topicPopover.hotTopics.map(t => t.id))
            const newTopics = topics.filter(t => !existingIds.has(t.id))

            this.topicPopover.hotTopics = [...this.topicPopover.hotTopics, ...newTopics]
            this.topicPopover.page++

            // 如果没有新话题，标记为没有更多
            if (newTopics.length === 0) {
              this.topicPopover.hasMore = false
            }
          }
        } else {
          console.warn('加载热门话题失败:', response)
          this.topicPopover.hasMore = false
        }
      } catch (error) {
        console.error('加载热门话题失败:', error)
        this.topicPopover.hasMore = false
      } finally {
        this.topicPopover.loading = false
      }
    },

    async searchTopics(keyword) {
      // 强制重置状态，确保每次都触发新的搜索
      this.topicPopover.searchKeyword = keyword
      this.topicPopover.searchTopics = []
      this.topicPopover.searchPage = 1
      this.topicPopover.searchHasMore = true

      // 如果关键字为空，也要触发搜索（显示热门话题或空结果）
      if (this.topicPopover.loading) {
        // 如果正在加载，取消之前的请求（这里简单处理，实际可以用AbortController）
        this.topicPopover.loading = false
      }

      this.topicPopover.loading = true
      try {
        const response = await this.request.getTopic({
          action: '201023',
          title: keyword,
          highlightTitle: 'title',
          page: this.topicPopover.searchPage,
          limit: 50
        })

        if (response && response.data && response.data.code === 0) {
          const topics = response.data.data || []
          this.topicPopover.searchTopics = topics
          this.topicPopover.searchPage++

          if (topics.length === 0) {
            this.topicPopover.searchHasMore = false
          } else {
            this.topicPopover.searchHasMore = topics.length >= 50 // 如果返回满页，可能还有更多
          }
        } else {
          console.warn('搜索话题失败:', response)
          this.topicPopover.searchHasMore = false
        }
      } catch (error) {
        console.error('搜索话题失败:', error)
        this.topicPopover.searchHasMore = false
      } finally {
        this.topicPopover.loading = false
      }
    },

    loadRecentTopics() {
      try {
        const localTopics = JSON.parse(localStorage.getItem('localTopic') || '[]')
        this.topicPopover.recentTopics = localTopics
      } catch (error) {
        console.error('加载最近使用话题失败:', error)
        this.topicPopover.recentTopics = []
      }
    },

    selectTopic(topic) {
      // 保存到最近使用
      this.saveToRecentTopics(topic)

      // 插入话题到编辑器
      this.insertTopicToEditor(topic)

      // 隐藏弹框
      this.hideTopicPopover()
    },

    saveToRecentTopics(topic) {
      try {
        let recentTopics = JSON.parse(localStorage.getItem('localTopic') || '[]')

        // 移除已存在的相同话题
        recentTopics = recentTopics.filter(item => item.id !== topic.id)

        // 添加到开头
        recentTopics.unshift(topic)

        // 限制最多保存20个
        if (recentTopics.length > 20) {
          recentTopics = recentTopics.slice(0, 20)
        }

        localStorage.setItem('localTopic', JSON.stringify(recentTopics))
      } catch (error) {
        console.error('保存最近使用话题失败:', error)
      }
    },

    insertTopicToEditor(topic) {
      // 先隐藏弹框，避免range变化
      const originalRange = this.topicPopover.originalRange
      const triggerInfo = this.topicPopover.triggerInfo
      const popoverType = this.topicPopover.type

      this.hideTopicPopover()

      // 使用保存的原始range或当前selection
      let range = originalRange
      if (!range) {
        const selection = window.getSelection()
        if (selection.rangeCount === 0) return
        range = selection.getRangeAt(0)
      }

      const topicText = `#${topic.exactlyMatchTitle}`

      // 根据触发信息删除已输入的内容
      if (triggerInfo && triggerInfo.paragraph) {
        const paragraph = triggerInfo.paragraph
        const hashIndex = triggerInfo.hashIndex

        // 创建新的range来删除从#号开始的内容
        const deleteRange = document.createRange()
        const walker = document.createTreeWalker(
          paragraph,
          NodeFilter.SHOW_TEXT,
          {
            acceptNode: function(node) {
              // 跳过mdd-topic元素内的文本节点
              let parent = node.parentNode
              while (parent && parent !== paragraph) {
                if (parent.tagName === 'MDD-TOPIC') {
                  return NodeFilter.FILTER_REJECT
                }
                parent = parent.parentNode
              }
              return NodeFilter.FILTER_ACCEPT
            }
          },
          false
        )

        let position = 0
        let startNode = null
        let startOffset = 0
        let endNode = null
        let endOffset = 0
        let node

        // 找到#号的位置
        while (node = walker.nextNode()) {
          if (position + node.textContent.length > hashIndex) {
            startNode = node
            startOffset = hashIndex - position
            break
          }
          position += node.textContent.length
        }

        // 找到当前光标位置
        const currentCursorPosition = triggerInfo.cursorPosition
        position = 0
        walker.currentNode = paragraph

        while (node = walker.nextNode()) {
          if (position + node.textContent.length >= currentCursorPosition) {
            endNode = node
            endOffset = currentCursorPosition - position
            break
          }
          position += node.textContent.length
        }

        if (startNode && endNode) {
          deleteRange.setStart(startNode, startOffset)
          deleteRange.setEnd(endNode, endOffset)
          deleteRange.deleteContents()

          // 设置新的插入位置
          range = document.createRange()
          range.setStart(startNode, startOffset)
          range.collapse(true)
        }
      } else if (popoverType === 'hot') {
        // 热门话题模式，需要删除刚输入的#号
        const container = range.startContainer
        if (container.nodeType === Node.TEXT_NODE && container.textContent) {
          const offset = range.startOffset
          if (offset > 0 && container.textContent[offset - 1] === '#') {
            // 删除#号
            const deleteRange = document.createRange()
            deleteRange.setStart(container, offset - 1)
            deleteRange.setEnd(container, offset)
            deleteRange.deleteContents()

            // 更新range位置
            range.setStart(container, offset - 1)
            range.collapse(true)
          }
        }
      }

      // 创建话题元素
      const topicElement = document.createElement('mdd-topic')
      topicElement.setAttribute('data-topic', JSON.stringify({
        topicId: topic.id,
        topicType: 0,
        startIndex: 0, // 会在updateTopicPosition中更新
        endIndex: 0    // 会在updateTopicPosition中更新
      }))
      topicElement.textContent = topicText

      // 设置mdd-topic元素为不可编辑
      topicElement.setAttribute('contenteditable', 'false')

      // 检查插入位置，如果在话题空格span内部，需要调整到span外面
      const insertionRange = this.adjustInsertionPosition(range)

      // 删除选中的内容
      insertionRange.deleteContents()

      // 插入话题元素
      insertionRange.insertNode(topicElement)

      // 在话题后面插入一个空格（使用&nbsp;）
      const spaceNode = document.createTextNode('\u00A0') // 不间断空格
      const spaceRange = document.createRange()
      spaceRange.setStartAfter(topicElement)
      spaceRange.insertNode(spaceNode)

      this.cursorPosition(spaceNode)
      setTimeout(() => this.cursorPosition(spaceNode), 0) // 延迟确保光标位置正确

      // 更新话题位置信息
      this.updateTopicPosition()
      this.updateData(true)
    },

    cursorPosition(target) {
      // 设置光标位置到空格后面
      const newRange = document.createRange()
      newRange.setStartAfter(target)
      newRange.collapse(true)

      // 恢复selection
      const selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(newRange)
    },

    handleTopicInput(event) {
      // 不在input或textarea中处理
      const activeElement = document.activeElement
      if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        return
      }

      const selection = window.getSelection()
      if (selection.rangeCount === 0) return

      const range = selection.getRangeAt(0)
      const container = range.startContainer

      // 确保在编辑器内
      if (!this.editorDom.contains(container)) return

      // 检查是否在mdd-topic元素内，如果是则不处理
      let currentNode = container
      while (currentNode && currentNode !== this.editorDom) {
        if (currentNode.nodeType === Node.ELEMENT_NODE && currentNode.tagName === 'MDD-TOPIC') {
          return
        }
        currentNode = currentNode.parentNode
      }

      // 获取当前段落
      let paragraph = container
      while (paragraph && paragraph.nodeType !== Node.ELEMENT_NODE) {
        paragraph = paragraph.parentNode
      }
      while (paragraph && !paragraph.classList?.contains('halo-paragraph')) {
        paragraph = paragraph.parentNode
      }

      if (!paragraph) return

      // 获取不包含mdd-topic内部文本的段落文本
      const { textContent: paragraphText, cursorPosition } = this.getParagraphTextExcludingTopics(paragraph, range)

      if (event.key === '#') {
        // 检查是否是 # 或 #@ 这种情况，不触发弹框
        const afterCursor = paragraphText.substring(cursorPosition)

        // 如果#号后面紧跟@或空格，则不触发
        if (afterCursor.length > 0 && (afterCursor[0] === '@' || afterCursor[0] === ' ')) {
          return
        }

        // 输入#号，显示热门话题弹框
        setTimeout(() => {
          const position = this.getCaretPosition()
          const triggerInfo = {
            paragraph: paragraph,
            hashIndex: cursorPosition, // #号的位置
            cursorPosition: cursorPosition + 1 // #号后的位置
          }
          this.showTopicPopover('hot', position, '', triggerInfo)
        }, 10)
      } else if (this.shouldTriggerSearch(event.key)) {
        // 输入其他字符或删除字符，检查是否需要显示搜索弹框
        setTimeout(() => {
          this.checkAndTriggerSearch(paragraph)
        }, 10)
      }
    },

    shouldTriggerSearch(key) {
      // 可打印字符、退格键、删除键都可能触发搜索
      return key.length === 1 || key === 'Backspace' || key === 'Delete'
    },

    checkAndTriggerSearch(paragraph) {
      const selection = window.getSelection()
      if (selection.rangeCount === 0) return

      const range = selection.getRangeAt(0)

      // 检查光标是否在mdd-topic元素内部，如果是则不触发搜索
      let currentNode = range.startContainer
      while (currentNode && currentNode !== paragraph) {
        if (currentNode.nodeType === Node.ELEMENT_NODE && currentNode.tagName === 'MDD-TOPIC') {
          this.hideTopicPopover()
          return
        }
        currentNode = currentNode.parentNode
      }

      // 获取不包含mdd-topic内部文本的段落文本
      const { textContent: paragraphText, cursorPosition } = this.getParagraphTextExcludingTopics(paragraph, range)
      const beforeCursor = paragraphText.substring(0, cursorPosition)
      const hashIndex = beforeCursor.lastIndexOf('#')

      if (hashIndex !== -1) {
        const afterHash = beforeCursor.substring(hashIndex + 1)

        // 检查#号后面是否有空格或@符号，如果有则不触发
        if (afterHash.indexOf(' ') === -1 && afterHash.indexOf('@') === -1) {
          // 检查搜索关键字长度限制，允许空字符串触发搜索
          if (afterHash.length <= 15) {
            const position = this.getCaretPosition()
            const triggerInfo = {
              paragraph: paragraph,
              hashIndex: hashIndex,
              cursorPosition: cursorPosition
            }
            // 每次都触发搜索，包括空字符串
            this.showTopicPopover('search', position, afterHash, triggerInfo)
          } else if (afterHash.length > 15) {
            // 超过15个字符，隐藏弹框
            this.hideTopicPopover()
          }
        } else {
          // 有空格或@符号，隐藏弹框
          this.hideTopicPopover()
        }
      } else {
        // 没有找到#号，隐藏弹框
        this.hideTopicPopover()
      }
    },

    getParagraphTextExcludingTopics(paragraph, range) {
      let textContent = ''
      let cursorPosition = 0
      let foundCursor = false

      const walker = document.createTreeWalker(
        paragraph,
        NodeFilter.SHOW_ALL,
        {
          acceptNode: function(node) {
            // 跳过mdd-topic元素及其子节点
            if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'MDD-TOPIC') {
              return NodeFilter.FILTER_REJECT
            }
            return NodeFilter.FILTER_ACCEPT
          }
        },
        false
      )

      let node
      while (node = walker.nextNode()) {
        if (node.nodeType === Node.TEXT_NODE) {
          const nodeText = node.textContent

          // 检查光标是否在当前文本节点中
          if (!foundCursor && node === range.startContainer) {
            cursorPosition = textContent.length + range.startOffset
            foundCursor = true
          }

          textContent += nodeText
        }
      }

      return { textContent, cursorPosition }
    },

    getCursorPositionInParagraph(paragraph, range) {
      const walker = document.createTreeWalker(
        paragraph,
        NodeFilter.SHOW_TEXT,
        null,
        false
      )

      let position = 0
      let node

      while (node = walker.nextNode()) {
        if (node === range.startContainer) {
          return position + range.startOffset
        }
        position += node.textContent.length
      }

      return position
    },

    getCaretPosition() {
      const selection = window.getSelection()
      if (selection.rangeCount === 0) return { top: 0, left: 0 }

      const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()
      const editorRect = this.editorDom.getBoundingClientRect()

      return {
        top: rect.bottom - editorRect.top + this.editorDom.scrollTop,
        left: rect.left - editorRect.left
      }
    },

    handleTopicScroll(event) {
      if (this.topicPopover.activeTab !== 'hot') return

      const container = event.target
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight
      const clientHeight = container.clientHeight

      // 滚动到底部时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadHotTopics()
      }
    },

    handleSearchTopicScroll(event) {
      const container = event.target
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight
      const clientHeight = container.clientHeight

      // 滚动到底部时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.searchTopics(this.topicPopover.searchKeyword)
      }
    },
  },
  beforeRouteLeave(to, from, next) {
    next()
  },
}
</script>
<style lang="less">
@import 'editor.less';
</style>
